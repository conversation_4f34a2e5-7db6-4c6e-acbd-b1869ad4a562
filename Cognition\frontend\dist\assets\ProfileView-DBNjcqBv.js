import{d as N,u as Y,r as m,c as U,I as j,o as T,b as i,e,f,t as v,k as I,w as d,A as _,n as p,a3 as h,F as O,x as R,B as q,C as X,D,g as F,a as $,l as r,_ as G}from"./index-tHlaP7TV.js";import{d as J}from"./dayjs.min-CxMP4GVf.js";const K={class:"profile-container"},Q={class:"profile-content"},W={class:"avatar-section"},Z={class:"avatar-container"},ee={class:"avatar-large"},ae=["src","alt"],se={key:0,class:"change-avatar-btn","aria-label":"更换头像"},le={class:"user-basic-info"},oe={class:"user-name"},te={class:"patient-number"},ne={class:"time-info"},ie={class:"join-date"},de={class:"last-login"},re={class:"info-form-section"},ue={class:"form-section"},ve={class:"form-row-three"},ce={class:"form-group"},me=["readonly"],pe={class:"form-group"},fe=["readonly"],be={class:"form-group"},ge=["disabled"],_e={class:"form-row-three"},he={class:"form-group"},ye=["disabled"],Ce=["value"],we={class:"form-group"},ke=["readonly","placeholder"],Be={class:"form-group"},xe=["disabled"],Me={class:"form-row-three"},Ve={class:"form-group"},Pe={class:"radio-group"},He={class:"radio-options"},Se={class:"radio-label"},Ue=["disabled"],De={class:"radio-label"},Fe=["disabled"],ze={key:0,class:"form-actions"},Ae=["disabled"],Le=["disabled","aria-busy"],Ee={key:0,class:"loading-spinner","aria-hidden":"true"},Ne={key:0,role:"alert",class:"success-toast","aria-live":"polite"},Ye={key:1,role:"alert",class:"error-message","aria-live":"polite"},je=N({__name:"ProfileView",setup(Te){const y=Y(),C=$(),z=()=>{window.history.length>1?C.back():C.push("/home")},l=m(!1),u=m(!1),g=m(!1),c=m(""),s=m({}),b=m({}),t=U(()=>y.user),w=U(()=>t.value?Object.keys(s.value).some(n=>{const a=n;return s.value[a]!==b.value[a]}):!1),k=n=>n?J(n).format("YYYY年MM月DD日 HH:mm"):"-",A=n=>n==="female"?"/images/Female.png":"/images/Male.png",B=()=>{t.value&&(s.value={name:t.value.name,gender:t.value.gender,education:t.value.education,contactPhone:t.value.contactPhone,idCard:t.value.idCard,dominantHand:t.value.dominantHand,isColorBlind:t.value.isColorBlind},b.value={...s.value})},L=()=>{l.value=!1,s.value={...b.value},c.value=""},E=async()=>{if(!w.value){l.value=!1;return}u.value=!0,c.value="";try{const n=await y.updateProfile(s.value);n.success?(l.value=!1,b.value={...s.value},g.value=!0,setTimeout(()=>{g.value=!1},3e3)):c.value=n.message}catch(n){c.value="保存失败，请稍后重试",console.error("Save profile error:",n)}finally{u.value=!1}};return j(t,n=>{n&&!l.value&&B()},{immediate:!0}),T(()=>{B()}),(n,a)=>{var x,M,V,P,H,S;return r(),i("div",K,[e("header",{class:"profile-header"},[e("button",{onClick:z,class:"back-button","aria-label":"返回"},a[8]||(a[8]=[e("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"})],-1)])),a[9]||(a[9]=e("h1",{class:"page-title"},"个人信息",-1))]),e("main",Q,[e("section",W,[e("div",Z,[e("div",ee,[e("img",{src:A((x=t.value)==null?void 0:x.gender),alt:`${(M=t.value)==null?void 0:M.name}的头像`,class:"avatar-img"},null,8,ae)]),l.value?(r(),i("button",se,a[10]||(a[10]=[e("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"currentColor"},[e("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)]))):f("",!0)]),e("div",le,[e("h2",oe,v((V=t.value)==null?void 0:V.name),1),e("p",te,"患者编号："+v((P=t.value)==null?void 0:P.patientNumber),1),e("div",ne,[e("span",ie,"注册时间："+v(k((H=t.value)==null?void 0:H.createdAt)),1),e("span",de,"最后登录："+v(k((S=t.value)==null?void 0:S.lastLoginAt)),1)])])]),e("section",re,[e("form",{onSubmit:I(E,["prevent"]),class:"profile-form"},[e("div",ue,[a[24]||(a[24]=e("h3",{class:"section-title"},"用户信息",-1)),e("div",ve,[e("div",ce,[a[11]||(a[11]=e("label",{for:"name",class:"label"},"姓名",-1)),d(e("input",{id:"name","onUpdate:modelValue":a[0]||(a[0]=o=>s.value.name=o),type:"text",class:p(["input",{readonly:!l.value}]),readonly:!l.value,required:"",maxlength:"20"},null,10,me),[[_,s.value.name]])]),e("div",pe,[a[12]||(a[12]=e("label",{for:"contactPhone",class:"label"},"联系电话",-1)),d(e("input",{id:"contactPhone","onUpdate:modelValue":a[1]||(a[1]=o=>s.value.contactPhone=o),type:"tel",class:p(["input",{readonly:!l.value}]),readonly:!l.value,pattern:"[0-9]{11}"},null,10,fe),[[_,s.value.contactPhone]])]),e("div",be,[a[14]||(a[14]=e("label",{for:"gender",class:"label"},"性别",-1)),d(e("select",{id:"gender","onUpdate:modelValue":a[2]||(a[2]=o=>s.value.gender=o),class:p(["input",{readonly:!l.value}]),disabled:!l.value},a[13]||(a[13]=[e("option",{value:"male"},"男",-1),e("option",{value:"female"},"女",-1)]),10,ge),[[h,s.value.gender]])])]),e("div",_e,[e("div",he,[a[15]||(a[15]=e("label",{for:"education",class:"label"},"学历",-1)),d(e("select",{id:"education","onUpdate:modelValue":a[3]||(a[3]=o=>s.value.education=o),class:p(["input",{readonly:!l.value}]),disabled:!l.value},[(r(!0),i(O,null,R(q(X),o=>(r(),i("option",{key:o.value,value:o.value},v(o.label),9,Ce))),128))],10,ye),[[h,s.value.education]])]),e("div",we,[a[16]||(a[16]=e("label",{for:"idCard",class:"label"},"身份证号",-1)),d(e("input",{id:"idCard","onUpdate:modelValue":a[4]||(a[4]=o=>s.value.idCard=o),type:"text",class:p(["input",{readonly:!l.value}]),readonly:!l.value,pattern:"[0-9X]{18}",maxlength:"18",placeholder:l.value?"请输入18位身份证号":""},null,10,ke),[[_,s.value.idCard]])]),e("div",Be,[a[18]||(a[18]=e("label",{for:"dominantHand",class:"label"},"惯用手",-1)),d(e("select",{id:"dominantHand","onUpdate:modelValue":a[5]||(a[5]=o=>s.value.dominantHand=o),class:p(["input",{readonly:!l.value}]),disabled:!l.value},a[17]||(a[17]=[e("option",{value:"right"},"右手",-1),e("option",{value:"left"},"左手",-1)]),10,xe),[[h,s.value.dominantHand]])])]),e("div",Me,[e("div",Ve,[e("fieldset",Pe,[a[21]||(a[21]=e("legend",{class:"label"},"是否色盲",-1)),e("div",He,[e("label",Se,[d(e("input",{type:"radio","onUpdate:modelValue":a[6]||(a[6]=o=>s.value.isColorBlind=o),value:!1,disabled:!l.value,name:"colorBlind",class:"radio"},null,8,Ue),[[D,s.value.isColorBlind]]),a[19]||(a[19]=e("span",{class:"radio-text"},"否",-1))]),e("label",De,[d(e("input",{type:"radio","onUpdate:modelValue":a[7]||(a[7]=o=>s.value.isColorBlind=o),value:!0,disabled:!l.value,name:"colorBlind",class:"radio"},null,8,Fe),[[D,s.value.isColorBlind]]),a[20]||(a[20]=e("span",{class:"radio-text"},"是",-1))])])])]),a[22]||(a[22]=e("div",{class:"form-group"},null,-1)),a[23]||(a[23]=e("div",{class:"form-group"},null,-1))])]),l.value?(r(),i("div",ze,[e("button",{type:"button",onClick:L,class:"btn btn-outline",disabled:u.value}," 取消 ",8,Ae),e("button",{type:"submit",class:"btn btn-primary",disabled:u.value||!w.value,"aria-busy":u.value},[u.value?(r(),i("span",Ee)):f("",!0),F(" "+v(u.value?"保存中...":"保存修改"),1)],8,Le)])):f("",!0)],32)])]),g.value?(r(),i("div",Ne,a[25]||(a[25]=[e("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"})],-1),F(" 个人信息已更新 ",-1)]))):f("",!0),c.value?(r(),i("div",Ye,v(c.value),1)):f("",!0)])}}}),Re=G(je,[["__scopeId","data-v-e5bf7de4"]]);export{Re as default};
