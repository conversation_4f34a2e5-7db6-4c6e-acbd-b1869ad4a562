import{d as at,u as lt,r as h,c as g,o as it,b as a,e as t,f as v,n as b,t as i,w as N,a3 as F,y as Q,F as w,x as C,h as nt,i as rt,j as dt,k as W,W as ut,X as vt,g as A,a0 as ct,l,_ as pt}from"./index-tHlaP7TV.js";import{d as T}from"./dayjs.min-CxMP4GVf.js";const ht={class:"test-history-container"},mt={class:"history-header"},gt=["aria-expanded"],ft={class:"history-content"},_t={class:"stats-section"},bt={class:"stats-grid"},yt={class:"stat-card"},kt={class:"stat-content"},wt={class:"stat-number"},Ct={class:"stat-card"},Mt={class:"stat-content"},Tt={class:"stat-number"},zt={class:"stat-card"},xt={class:"stat-content"},Vt={class:"stat-number"},Ht={key:0,class:"filters-section"},St={class:"filters-card"},$t={class:"filters-grid"},Dt={class:"filter-group"},Bt={class:"filter-group"},Lt=["value"],Rt={class:"filter-group"},Nt={class:"filters-actions"},Ft={class:"records-section"},At={class:"section-header"},Pt={class:"section-title"},Ut={class:"view-toggle"},Yt={key:0,class:"empty-state"},jt={class:"empty-description"},qt={key:1,class:"tests-list"},Gt={class:"test-main"},Qt={class:"test-info"},Wt={class:"test-name"},Et={class:"test-meta"},It={class:"test-date"},Xt={key:0,class:"test-duration"},Jt={class:"test-result"},Kt=["aria-label"],Ot={key:0,class:"test-score"},Zt={class:"test-actions"},ts=["onClick"],ss={key:1,class:"status-text"},es={key:2,class:"tests-grid"},os={class:"card-header"},as={class:"test-type-badge"},ls=["aria-label"],is={class:"card-content"},ns={class:"test-name"},rs={class:"test-meta"},ds={class:"meta-item"},us={key:0,class:"meta-item"},vs={class:"card-footer"},cs={key:0,class:"test-score"},ps=["onClick"],hs={key:2,class:"status-text"},ms={key:3,class:"tests-grouped"},gs=["onClick"],fs={class:"date-info"},_s={class:"date-title"},bs={class:"date-count"},ys={class:"date-stats"},ks={class:"completion-rate"},ws={class:"rate-value"},Cs=["onClick"],Ms={key:0,class:"date-records"},Ts={class:"record-main"},zs={class:"record-info"},xs={class:"record-name"},Vs={class:"record-meta"},Hs={class:"record-time"},Ss={key:0,class:"record-duration"},$s={key:1,class:"record-score"},Ds={class:"record-actions"},Bs=["onClick"],Ls={key:1,class:"status-text"},Rs={key:4,class:"pagination"},Ns=["disabled"],Fs={class:"pagination-info"},As=["disabled"],Ps={class:"modal-header"},Us={class:"modal-title"},Ys={class:"modal-body"},js={key:0,class:"report-content"},qs={class:"score-section"},Gs={class:"score-circle"},Qs={class:"score-value"},Ws={class:"score-description"},Es={class:"analysis-section"},Is={key:0,class:"recommendations-section"},Xs={class:"recommendations-list"},Js=at({__name:"TestHistoryView",setup(Ks){const E=lt(),M=h(!1),m=h("list"),f=h(1),S=h(10),y=h([]),P=h({}),z=h(null),x=h(!1),p=h(null),r=h({status:"",testType:"",dateRange:""}),$=g(()=>E.user),I=g(()=>y.value.filter(e=>e.status==="completed").length),X=g(()=>{const e=y.value.filter(n=>n.status==="completed"&&n.score!==void 0);if(e.length===0)return 0;const s=e.reduce((n,c)=>n+(c.score||0),0);return Math.round(s/e.length)}),J=g(()=>{const e=y.value.filter(s=>s.duration).reduce((s,n)=>s+(n.duration||0),0);return Math.round(e/3600*10)/10}),K=g(()=>{const e=new Set(y.value.map(s=>s.testType));return Array.from(e)}),U=g(()=>r.value.status||r.value.testType||r.value.dateRange),V=g(()=>{let e=[...y.value];if(r.value.status&&(e=e.filter(s=>s.status===r.value.status)),r.value.testType&&(e=e.filter(s=>s.testType===r.value.testType)),r.value.dateRange){const s=T();e=e.filter(n=>{const c=T(n.startTime);switch(r.value.dateRange){case"today":return c.isSame(s,"day");case"week":return c.isAfter(s.subtract(7,"day"));case"month":return c.isAfter(s.subtract(1,"month"));case"quarter":return c.isAfter(s.subtract(3,"month"));default:return!0}})}return e.sort((s,n)=>new Date(n.startTime).getTime()-new Date(s.startTime).getTime())}),D=g(()=>Math.ceil(V.value.length/S.value)),Y=g(()=>{const e=(f.value-1)*S.value,s=e+S.value;return V.value.slice(e,s)}),j=e=>T(e).format("MM月DD日 HH:mm"),B=e=>{const s=Math.floor(e/60),n=e%60;return`${s}分${n}秒`},_=e=>({pending:"待开始",inProgress:"进行中",completed:"已完成",failed:"失败"})[e]||e,q=e=>({PDQ5:"PDQ-5",Hopkins:"Hopkins词汇学习",NBack:"顺背/倒背测试",Stroop:"Stroop色词测试",TrailMaking:"连线测试",VerbalFluency:"词语流畅性",CPT:"持续性操作测试",DSST:"DSST数字符号",MaskedPriming:"掩蔽情感启动"})[e]||e,O=()=>{r.value={status:"",testType:"",dateRange:""},f.value=1},L=e=>{if(e.status!=="completed")return;const s=ct(e);s&&(p.value=s,x.value=!0)},R=()=>{x.value=!1,p.value=null},Z=e=>e>=90?"优秀！您的认知能力表现非常出色。":e>=80?"良好！您的认知能力表现不错。":e>=70?"一般！您的认知能力表现中等。":e>=60?"需要改进！建议加强相关训练。":"需要关注！建议咨询专业医生。",tt=e=>{z.value=z.value===e?null:e},H=e=>T(e).format("YYYY年MM月DD日"),st=e=>T(e).format("HH:mm"),G=e=>{const s=e.filter(n=>n.status==="completed").length;return e.length>0?Math.round(s/e.length*100):0},et=(e,s)=>{const n=s.filter(d=>d.status==="completed");if(n.length===0)return;const c=n.reduce((d,u)=>d+(u.score||0),0),k=Math.round(c/n.length),o={isTaskReport:!0,taskName:`${H(e)} 测试任务`,testName:`${H(e)} 测试任务`,testCount:n.length,score:k,percentile:Math.floor(Math.random()*40)+60,analysis:`基于 ${n.length} 个测试项目的综合分析显示，您在${H(e)}的整体表现${k>80?"优秀":k>70?"良好":"中等"}。各项认知能力指标表现均衡。`,recommendations:["恭喜完成当日的认知能力评估","建议定期进行复测以跟踪认知能力变化","可以针对薄弱环节进行专项训练","保持健康的生活方式有助于认知能力维护"]};p.value=o,x.value=!0},ot=()=>{$.value&&(y.value=ut($.value.id),P.value=vt($.value.id))};return it(()=>{ot()}),(e,s)=>{var c,k;const n=dt("router-link");return l(),a("div",ht,[t("header",mt,[t("button",{onClick:s[0]||(s[0]=o=>e.$router.back()),class:"back-button","aria-label":"返回"},s[12]||(s[12]=[t("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"currentColor"},[t("path",{d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"})],-1)])),s[14]||(s[14]=t("h1",{class:"page-title"},"测试记录",-1)),t("button",{onClick:s[1]||(s[1]=o=>M.value=!M.value),class:b(["filter-button",{active:U.value}]),"aria-expanded":M.value,"aria-label":"筛选选项"},s[13]||(s[13]=[t("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"currentColor"},[t("path",{d:"M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z"})],-1)]),10,gt)]),t("main",ft,[t("section",_t,[t("div",bt,[t("div",yt,[s[16]||(s[16]=t("div",{class:"stat-icon completed"},[t("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor"},[t("path",{d:"M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"})])],-1)),t("div",kt,[t("div",wt,i(I.value),1),s[15]||(s[15]=t("div",{class:"stat-label"},"已完成",-1))])]),t("div",Ct,[s[18]||(s[18]=t("div",{class:"stat-icon average"},[t("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor"},[t("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})])],-1)),t("div",Mt,[t("div",Tt,i(X.value)+"%",1),s[17]||(s[17]=t("div",{class:"stat-label"},"平均得分",-1))])]),t("div",zt,[s[20]||(s[20]=t("div",{class:"stat-icon time"},[t("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor"},[t("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})])],-1)),t("div",xt,[t("div",Vt,i(J.value)+"h",1),s[19]||(s[19]=t("div",{class:"stat-label"},"总时长",-1))])])])]),M.value?(l(),a("section",Ht,[t("div",St,[s[27]||(s[27]=t("h3",{class:"filters-title"},"筛选条件",-1)),t("div",$t,[t("div",Dt,[s[22]||(s[22]=t("label",{for:"statusFilter",class:"filter-label"},"状态",-1)),N(t("select",{id:"statusFilter","onUpdate:modelValue":s[2]||(s[2]=o=>r.value.status=o),class:"filter-select"},s[21]||(s[21]=[Q('<option value="" data-v-76e4ed2f>全部状态</option><option value="completed" data-v-76e4ed2f>已完成</option><option value="inProgress" data-v-76e4ed2f>进行中</option><option value="pending" data-v-76e4ed2f>待开始</option><option value="failed" data-v-76e4ed2f>失败</option>',5)]),512),[[F,r.value.status]])]),t("div",Bt,[s[24]||(s[24]=t("label",{for:"typeFilter",class:"filter-label"},"测试类型",-1)),N(t("select",{id:"typeFilter","onUpdate:modelValue":s[3]||(s[3]=o=>r.value.testType=o),class:"filter-select"},[s[23]||(s[23]=t("option",{value:""},"全部类型",-1)),(l(!0),a(w,null,C(K.value,o=>(l(),a("option",{key:o,value:o},i(q(o)),9,Lt))),128))],512),[[F,r.value.testType]])]),t("div",Rt,[s[26]||(s[26]=t("label",{for:"dateFilter",class:"filter-label"},"时间范围",-1)),N(t("select",{id:"dateFilter","onUpdate:modelValue":s[4]||(s[4]=o=>r.value.dateRange=o),class:"filter-select"},s[25]||(s[25]=[Q('<option value="" data-v-76e4ed2f>全部时间</option><option value="today" data-v-76e4ed2f>今天</option><option value="week" data-v-76e4ed2f>最近一周</option><option value="month" data-v-76e4ed2f>最近一月</option><option value="quarter" data-v-76e4ed2f>最近三月</option>',5)]),512),[[F,r.value.dateRange]])])]),t("div",Nt,[t("button",{onClick:O,class:"btn btn-outline btn-sm"}," 重置 "),t("button",{onClick:s[5]||(s[5]=o=>M.value=!1),class:"btn btn-primary btn-sm"}," 确定 ")])])])):v("",!0),t("section",Ft,[t("div",At,[t("h2",Pt," 测试记录 ("+i(V.value.length)+") ",1),t("div",Ut,[t("button",{onClick:s[6]||(s[6]=o=>m.value="list"),class:b(["toggle-btn",{active:m.value==="list"}]),"aria-label":"列表视图"},s[28]||(s[28]=[t("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"currentColor"},[t("path",{d:"M2 3h12a1 1 0 010 2H2a1 1 0 010-2zm0 4h12a1 1 0 010 2H2a1 1 0 010-2zm0 4h12a1 1 0 010 2H2a1 1 0 010-2z"})],-1)]),2),t("button",{onClick:s[7]||(s[7]=o=>m.value="grid"),class:b(["toggle-btn",{active:m.value==="grid"}]),"aria-label":"网格视图"},s[29]||(s[29]=[t("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"currentColor"},[t("path",{d:"M1 1h6v6H1V1zm8 0h6v6H9V1zM1 9h6v6H1V9zm8 0h6v6H9V9z"})],-1)]),2),t("button",{onClick:s[8]||(s[8]=o=>m.value="grouped"),class:b(["toggle-btn",{active:m.value==="grouped"}]),"aria-label":"按日期分组"},s[30]||(s[30]=[t("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"currentColor"},[t("path",{d:"M3.5 0a.5.5 0 01.5.5V1h8V.5a.5.5 0 011 0V1h1a2 2 0 012 2v11a2 2 0 01-2 2H2a2 2 0 01-2-2V3a2 2 0 012-2h1V.5a.5.5 0 01.5 0zM2 2a1 1 0 00-1 1v1h14V3a1 1 0 00-1-1H2zM1 5v9a1 1 0 001 1h12a1 1 0 001-1V5H1z"})],-1)]),2)])]),V.value.length===0?(l(),a("div",Yt,[s[32]||(s[32]=t("div",{class:"empty-icon"},[t("svg",{width:"64",height:"64",viewBox:"0 0 64 64",fill:"currentColor",opacity:"0.3"},[t("path",{d:"M32 8C18.745 8 8 18.745 8 32s10.745 24 24 24 24-10.745 24-24S45.255 8 32 8zm-4 34H24v-4h4v4zm0-8H24V18h4v8zm8 8h-4v-4h4v4zm0-8h-4V18h4v8z"})])],-1)),s[33]||(s[33]=t("h3",{class:"empty-title"},"暂无测试记录",-1)),t("p",jt,i(U.value?"没有符合筛选条件的记录":"您还没有参加过任何测试"),1),nt(n,{to:"/tests",class:"btn btn-primary"},{default:rt(()=>s[31]||(s[31]=[A(" 开始测试 ",-1)])),_:1,__:[31]})])):m.value==="list"?(l(),a("div",qt,[(l(!0),a(w,null,C(Y.value,o=>(l(),a("div",{key:o.id,class:"test-item"},[t("div",Gt,[s[34]||(s[34]=t("div",{class:"test-icon"},[t("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor"},[t("path",{d:"M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.1 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z"})])],-1)),t("div",Qt,[t("h3",Wt,i(o.testName),1),t("div",Et,[t("span",It,i(j(o.startTime)),1),o.duration?(l(),a("span",Xt,i(B(o.duration)),1)):v("",!0)])])]),t("div",Jt,[t("span",{class:b(["status-badge",`status-${o.status}`]),"aria-label":`测试状态：${_(o.status)}`},i(_(o.status)),11,Kt),o.score!==void 0?(l(),a("span",Ot,i(o.score)+"分 ",1)):v("",!0)]),t("div",Zt,[o.status==="completed"?(l(),a("button",{key:0,onClick:d=>L(o),class:"view-result-btn"}," 📈 查看测试结果 ",8,ts)):(l(),a("span",ss,i(_(o.status)),1))])]))),128))])):m.value==="grid"?(l(),a("div",es,[(l(!0),a(w,null,C(Y.value,o=>(l(),a("div",{key:o.id,class:"test-card"},[t("div",os,[t("div",as,i(q(o.testType)),1),t("span",{class:b(["status-badge",`status-${o.status}`]),"aria-label":`测试状态：${_(o.status)}`},i(_(o.status)),11,ls)]),t("div",is,[t("h3",ns,i(o.testName),1),t("div",rs,[t("div",ds,[s[35]||(s[35]=t("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"currentColor"},[t("path",{d:"M7 0C3.134 0 0 3.134 0 7s3.134 7 7 7 7-3.134 7-7-3.134-7-7-7zm0 12.6C3.685 12.6 1.4 10.315 1.4 7S3.685 1.4 7 1.4 12.6 3.685 12.6 7 10.315 12.6 7 12.6z"}),t("path",{d:"M7.7 3.5H6.3v4.2l3.15 1.89.7-1.155L7.7 6.65V3.5z"})],-1)),A(" "+i(j(o.startTime)),1)]),o.duration?(l(),a("div",us,[s[36]||(s[36]=t("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"currentColor"},[t("path",{d:"M7 0C3.134 0 0 3.134 0 7s3.134 7 7 7 7-3.134 7-7-3.134-7-7-7zm0 12.6C3.685 12.6 1.4 10.315 1.4 7S3.685 1.4 7 1.4 12.6 3.685 12.6 7 10.315 12.6 7 12.6z"})],-1)),A(" "+i(B(o.duration)),1)])):v("",!0)])]),t("div",vs,[o.score!==void 0?(l(),a("span",cs," 得分："+i(o.score)+"分 ",1)):v("",!0),o.status==="completed"?(l(),a("button",{key:1,onClick:d=>L(o),class:"view-detail-btn","aria-label":"查看测试结果"}," 📈 查看测试结果 ",8,ps)):(l(),a("span",hs,i(_(o.status)),1))])]))),128))])):m.value==="grouped"?(l(),a("div",ms,[(l(!0),a(w,null,C(P.value,(o,d)=>(l(),a("div",{key:d,class:"date-group"},[t("div",{class:"date-header",onClick:u=>tt(d)},[t("div",fs,[t("h3",_s,i(H(d)),1),t("span",bs,i(o.length)+" 次测试",1)]),t("div",ys,[t("div",ks,[s[37]||(s[37]=t("span",{class:"rate-text"},"完成率",-1)),t("span",ws,i(G(o))+"%",1)]),G(o)===100?(l(),a("button",{key:0,onClick:W(u=>et(d,o),["stop"]),class:"task-report-btn"}," 📊 查看测试报告 ",8,Cs)):v("",!0),t("button",{class:b(["expand-btn",{expanded:z.value===d}])},s[38]||(s[38]=[t("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"currentColor"},[t("path",{d:"M1.646 4.646a.5.5 0 01.708 0L8 10.293l5.646-5.647a.5.5 0 01.708.708l-6 6a.5.5 0 01-.708 0l-6-6a.5.5 0 010-.708z"})],-1)]),2)])],8,gs),z.value===d?(l(),a("div",Ms,[(l(!0),a(w,null,C(o,u=>(l(),a("div",{key:u.id,class:"record-item"},[t("div",Ts,[s[39]||(s[39]=t("div",{class:"record-icon"},[t("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"currentColor"},[t("path",{d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),t("div",zs,[t("h4",xs,i(u.testName),1),t("div",Vs,[t("span",Hs,i(st(u.startTime)),1),u.duration?(l(),a("span",Ss,i(B(u.duration)),1)):v("",!0),u.score!==void 0?(l(),a("span",$s,i(u.score)+"分 ",1)):v("",!0)])])]),t("div",Ds,[u.status==="completed"?(l(),a("button",{key:0,onClick:Os=>L(u),class:"view-result-btn"}," 📈 查看测试结果 ",8,Bs)):(l(),a("span",Ls,i(_(u.status)),1))])]))),128))])):v("",!0)]))),128))])):v("",!0),D.value>1?(l(),a("div",Rs,[t("button",{onClick:s[9]||(s[9]=o=>f.value--),disabled:f.value===1,class:"pagination-btn","aria-label":"上一页"},s[40]||(s[40]=[t("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"currentColor"},[t("path",{d:"M11.354 1.646a.5.5 0 010 .708L5.707 8l5.647 5.646a.5.5 0 01-.708.708l-6-6a.5.5 0 010-.708l6-6a.5.5 0 01.708 0z"})],-1)]),8,Ns),t("div",Fs," 第 "+i(f.value)+" 页，共 "+i(D.value)+" 页 ",1),t("button",{onClick:s[10]||(s[10]=o=>f.value++),disabled:f.value===D.value,class:"pagination-btn","aria-label":"下一页"},s[41]||(s[41]=[t("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"currentColor"},[t("path",{d:"M4.646 1.646a.5.5 0 000 .708L10.293 8 4.646 14.354a.5.5 0 10.708.708l6-6a.5.5 0 000-.708l-6-6a.5.5 0 00-.708 0z"})],-1)]),8,As)])):v("",!0)])]),x.value?(l(),a("div",{key:0,class:"modal-overlay",onClick:R},[t("div",{class:"modal-content",onClick:s[11]||(s[11]=W(()=>{},["stop"]))},[t("div",Ps,[t("h2",Us,i((c=p.value)==null?void 0:c.testName)+" 测试报告",1),t("button",{onClick:R,class:"modal-close","aria-label":"关闭"},s[42]||(s[42]=[t("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor"},[t("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})],-1)]))]),t("div",Ys,[p.value?(l(),a("div",js,[t("div",qs,[t("div",Gs,[t("div",Qs,i(p.value.score),1),s[43]||(s[43]=t("div",{class:"score-label"},"总分",-1))]),t("div",Ws,[s[44]||(s[44]=t("h3",null,"测试表现",-1)),t("p",null,i(Z(p.value.score)),1)])]),t("div",Es,[s[45]||(s[45]=t("h3",null,"详细分析",-1)),t("p",null,i(p.value.analysis),1)]),(k=p.value.recommendations)!=null&&k.length?(l(),a("div",Is,[s[46]||(s[46]=t("h3",null,"建议",-1)),t("ul",Xs,[(l(!0),a(w,null,C(p.value.recommendations,(o,d)=>(l(),a("li",{key:d},i(o),1))),128))])])):v("",!0)])):v("",!0)]),t("div",{class:"modal-footer"},[t("button",{onClick:R,class:"btn btn-primary"}," 确定 ")])])])):v("",!0)])}}}),se=pt(Js,[["__scopeId","data-v-76e4ed2f"]]);export{se as default};
