import{d as ue,r,c as q,o as ce,m as de,b as s,e,f as b,h as W,s as ve,t as w,a6 as me,B as M,F as X,x as Y,y as pe,a as ge,n as he,l as a,_ as fe}from"./index-tHlaP7TV.js";import{G as V,a as ke}from"./GlobalTimer-BPkfVOeW.js";const ye={title:"掩蔽情感启动任务",content:["您将看到一个快速闪过的表情之后，紧接着出现一个持续时间较长的表情。您需要判断这个持续时间较长的表情传达的情绪是正面的（快乐）还是负面的（愤怒）。","请使用1-9的数字进行评分：1表示极度负面，9表示极度正面，5表示中性。越正面，按越大的数字；越负面，按越小的数字。","按任意键开始练习。"],audioEnabled:!0,startButtonText:"开始练习"},k=(y,m)=>{const n=[];return{AF:[1,2,3,4,5,6,7,8,10,15,16,17,18,24,27,28,30,31,33,34,36,37],AM:[1,6,7,8,9,10,11,12,13,17,18,19,22,24,26,27,29,30,32,33,34,35,36,37]}[m].forEach(h=>{const v=`/images/掩蔽情感启动任务/${y}/${m}${h}`;n.push(`${v}.webp`)}),n},d={happy:[...k("快乐","AF"),...k("快乐","AM")],angry:[...k("愤怒","AF"),...k("愤怒","AM")],neutral:[...k("平静1","AF"),...k("平静1","AM")],mask:[...k("平静2","AF"),...k("平静2","AM")]},x=y=>y[Math.floor(Math.random()*y.length)],Z=(y=!1)=>{const m=[];if(y)m.push({id:"practice_1",group:"A",stimulusImages:[],maskImages:[],targetEmotion:"happy"},{id:"practice_2",group:"B",stimulusImages:[],maskImages:[],targetEmotion:"angry"});else{const n=["A","B","C"];let I=1;for(let g=0;g<3;g++)for(let h=0;h<3;h++)for(let v=0;v<6;v++){const u=n[h];let l;u==="A"?l=v<3?"happy":"angry":u==="B"?l=v%2===0?"happy":"angry":l=v<2?"angry":"happy",m.push({id:`event_${I}`,group:u,stimulusImages:[],maskImages:[],targetEmotion:l}),I++}}return m.forEach(n=>{n.stimulusImages=[x(n.targetEmotion==="happy"?d.happy:d.angry),x(n.targetEmotion==="happy"?d.happy:d.angry),x(n.targetEmotion==="happy"?d.happy:d.angry)],n.maskImages=[x(d.mask),x(d.mask),x(d.mask)]}),m},be=[{value:1,text:"1",description:"极度负面"},{value:2,text:"2",description:"很负面"},{value:3,text:"3",description:"负面"},{value:4,text:"4",description:"稍微负面"},{value:5,text:"5",description:"中性"},{value:6,text:"6",description:"稍微正面"},{value:7,text:"7",description:"正面"},{value:8,text:"8",description:"很正面"},{value:9,text:"9",description:"极度正面"}],_={stimulusDuration:33,maskDuration:434,crossDuration:1e3,blackDuration:500,questionDuration:4e3,practiceEvents:2,totalEvents:54},_e={class:"test-container"},Ee={class:"header-section"},we={class:"header-center"},xe={class:"progress-info"},Ie={key:0,class:"progress-bar"},Te={key:1,class:"progress-text"},Ce={class:"header-right"},Me={key:0,class:"instructions-screen"},Le={class:"instructions-card"},Ae={class:"instructions-title"},De={class:"instructions-content"},Se={key:1,class:"instructions-paragraph"},qe={key:1,class:"test-screen"},Fe={key:0,class:"cross-display"},Pe={key:1,class:"black-screen"},$e={key:2,class:"stimulus-display"},Be=["src"],Ge={key:3,class:"mask-display"},Ne=["src"],Re={key:4,class:"question-display"},Ve={class:"question-content"},ze={class:"number-options"},je=["onClick","title"],He={key:0,class:"reminder-section"},Ke={key:1,class:"start-test-section"},Oe={key:2,class:"completion-screen"},Qe={key:3,class:"exit-dialog-overlay"},Ue=ue({__name:"MaskedEmotionTestView",setup(y){const m=ge(),n=r(!0),I=r(!1),g=r(!1),h=r(!1),v=r(!1),u=r(!0),l=r(0),c=r("cross"),T=r(0),E=r(null),F=r(!1),P=r(""),$=r(""),L=ye,B=r([]),G=r([]),A=q(()=>u.value?B.value:G.value),ee=q(()=>u.value?_.practiceEvents:_.totalEvents),te=q(()=>A.value.length===0?0:Math.round(l.value/A.value.length*100)),f=q(()=>A.value[l.value]);let p=null,D=null;const se=async()=>{const i=[...d.happy,...d.angry,...d.neutral,...d.mask];console.log("Starting to preload images:",i.length,"total images"),console.log("Sample image paths:",i.slice(0,5));const t=i.map(o=>new Promise(C=>{const R=new Image;R.onload=()=>{console.log(`✓ Successfully loaded: ${o}`),C(o)},R.onerror=()=>{console.warn(`✗ Failed to load image: ${o}`),C(o)},R.src=o}));try{await Promise.all(t),console.log("Images preloading completed")}catch(o){console.error("Error preloading images:",o)}};ce(async()=>{await se(),B.value=Z(!0),G.value=Z(!1),document.addEventListener("keydown",z),V.start()}),de(()=>{document.removeEventListener("keydown",z),S()});const S=()=>{p&&(clearTimeout(p),p=null),D&&(clearTimeout(D),D=null)},z=i=>{if(n.value)j();else if(c.value==="question"){const t=i.key;["1","2","3","4","5","6","7","8","9"].includes(t)?O(parseInt(t)):u.value&&l.value===1&&E.value&&H()}},j=()=>{n.value=!1,g.value=!0,u.value=!0,l.value=0,N()},H=()=>{I.value=!1,g.value=!0,u.value=!1,l.value=0,N()},N=()=>{f.value&&(f.value.onsetTime=Date.now(),T.value=0,E.value=null,F.value=!1,K())},K=()=>{c.value="cross",p=setTimeout(()=>{c.value="black",p=setTimeout(()=>{var i;c.value="stimulus",P.value=((i=f.value)==null?void 0:i.stimulusImages[T.value])||"",console.log(`Loading stimulus image: ${P.value}`),p=setTimeout(()=>{c.value="black",p=setTimeout(()=>{var t;c.value="mask",$.value=((t=f.value)==null?void 0:t.maskImages[T.value])||"",console.log(`Loading mask image: ${$.value}`),p=setTimeout(()=>{T.value++,T.value<3?K():ae()},_.maskDuration)},32)},_.stimulusDuration)},_.blackDuration)},_.crossDuration)},ae=()=>{c.value="question",D=setTimeout(()=>{F.value=!0},3e3),p=setTimeout(()=>{u.value&&l.value===1||Q()},_.questionDuration)},O=i=>{c.value!=="question"||!f.value||(E.value=i,f.value.response=i,f.value.responseTime=Date.now()-(f.value.onsetTime||0),S(),!(u.value&&l.value===1)&&setTimeout(()=>{Q()},300))},Q=()=>{l.value++,l.value>=A.value.length?u.value?S():oe():N()},oe=()=>{g.value=!1,h.value=!0,V.stop(),console.log("掩蔽情感启动任务完成"),console.log("练习结果:",B.value),console.log("正式测试结果:",G.value)},ne=()=>{v.value=!0},le=()=>{v.value=!1},ie=()=>{V.stop(),S(),m.push("/home")},re=()=>{m.push("/home")},U=i=>{const o=i.target.src;console.error("Failed to load webp image:",o)},J=i=>{const t=i.target;console.log("Image loaded successfully:",t.src)};return(i,t)=>(a(),s("div",_e,[e("div",Ee,[e("button",{onClick:ne,class:"exit-button","aria-label":"退出测试"},t[0]||(t[0]=[e("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M18 6L6 18M6 6L18 18",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)])),e("div",we,[W(ke,{position:"title-left",size:"medium"}),e("div",xe,[t[1]||(t[1]=e("h1",{class:"test-title"},"掩蔽情感启动任务",-1)),n.value?b("",!0):(a(),s("div",Ie,[e("div",{class:"progress-fill",style:ve({width:`${te.value}%`})},null,4)])),n.value?b("",!0):(a(),s("div",Te,w(l.value+1)+" / "+w(ee.value)+" 个事件 ",1))])]),e("div",Ce,[W(me)])]),n.value?(a(),s("div",Me,[e("div",Le,[e("h2",Ae,w(M(L).title),1),e("div",De,[Array.isArray(M(L).content)?(a(!0),s(X,{key:0},Y(M(L).content,(o,C)=>(a(),s("p",{key:C,class:"instructions-paragraph"},w(o),1))),128)):(a(),s("p",Se,w(M(L).content),1))]),e("div",{class:"start-hint"},[e("button",{onClick:j,class:"start-practice-button"}," 开始练习 ")])])])):g.value?(a(),s("div",qe,[c.value==="cross"?(a(),s("div",Fe,t[2]||(t[2]=[e("div",{class:"cross"},"+",-1)]))):c.value==="black"?(a(),s("div",Pe)):c.value==="stimulus"?(a(),s("div",$e,[e("img",{src:P.value,alt:"刺激图片",class:"stimulus-image",onError:U,onLoad:J},null,40,Be)])):c.value==="mask"?(a(),s("div",Ge,[e("img",{src:$.value,alt:"掩蔽图片",class:"mask-image",onError:U,onLoad:J},null,40,Ne)])):c.value==="question"?(a(),s("div",Re,[e("div",Ve,[t[4]||(t[4]=pe('<div class="question-header" data-v-dedb4eef><h2 class="question-title" data-v-dedb4eef>最后出现的表情传达的情绪是正面的还是负面的？</h2></div><div class="emotion-labels" data-v-dedb4eef><div class="emotion-label left anger" data-v-dedb4eef><span class="emotion-text" data-v-dedb4eef>负面</span><span class="emotion-subtitle" data-v-dedb4eef>愤怒</span></div><div class="emotion-arrow" data-v-dedb4eef><svg width="120" height="20" viewBox="0 0 120 20" fill="none" data-v-dedb4eef><defs data-v-dedb4eef><linearGradient id="arrowGradient" x1="0%" y1="0%" x2="100%" y2="0%" data-v-dedb4eef><stop offset="0%" style="stop-color:#ef4444;stop-opacity:1;" data-v-dedb4eef></stop><stop offset="100%" style="stop-color:#22c55e;stop-opacity:1;" data-v-dedb4eef></stop></linearGradient></defs><path d="M10 10L110 10" stroke="url(#arrowGradient)" stroke-width="2" data-v-dedb4eef></path><path d="M5 5L10 10L5 15" stroke="url(#arrowGradient)" stroke-width="2" fill="none" data-v-dedb4eef></path><path d="M115 5L110 10L115 15" stroke="url(#arrowGradient)" stroke-width="2" fill="none" data-v-dedb4eef></path></svg></div><div class="emotion-label right happiness" data-v-dedb4eef><span class="emotion-text" data-v-dedb4eef>正面</span><span class="emotion-subtitle" data-v-dedb4eef>快乐</span></div></div>',2)),e("div",ze,[(a(!0),s(X,null,Y(M(be),o=>(a(),s("div",{key:o.value,class:he(["number-button",{selected:E.value===o.value}]),onClick:C=>O(o.value),title:o.description},w(o.text),11,je))),128))]),F.value&&!E.value?(a(),s("div",He,t[3]||(t[3]=[e("div",{class:"reminder-text"}," 请选择！！！ ",-1)]))):b("",!0),u.value&&l.value===1&&E.value?(a(),s("div",Ke,[e("button",{onClick:H,class:"start-test-button"}," 开始正式测试 ")])):b("",!0)])])):b("",!0)])):h.value?(a(),s("div",Oe,[e("div",{class:"completion-card"},[t[5]||(t[5]=e("h2",null,"测试完成",-1)),t[6]||(t[6]=e("p",null,"感谢您完成掩蔽情感启动任务！",-1)),e("button",{onClick:re,class:"complete-button"},"返回首页")])])):b("",!0),v.value?(a(),s("div",Qe,[e("div",{class:"exit-dialog"},[t[7]||(t[7]=e("h3",null,"确认退出",-1)),t[8]||(t[8]=e("p",null,"您确定要退出测试吗？当前进度将会丢失。",-1)),e("div",{class:"dialog-buttons"},[e("button",{onClick:le,class:"cancel-button"},"取消"),e("button",{onClick:ie,class:"confirm-button"},"确认退出")])])])):b("",!0)]))}}),Xe=fe(Ue,[["__scopeId","data-v-dedb4eef"]]);export{Xe as default};
