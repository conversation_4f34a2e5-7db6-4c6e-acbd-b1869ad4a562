const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/LoginView-BbfaX9i7.js","assets/LoginView-Dy7DxPQh.css","assets/RegisterView-Du8vy0Gv.js","assets/RegisterView-CsK-epDY.css","assets/HomeView-BoU89C5E.js","assets/dayjs.min-CxMP4GVf.js","assets/HomeView-Dbo3NQ6s.css","assets/ProfileView-DBNjcqBv.js","assets/ProfileView-ByhQVOkC.css","assets/TestHistoryView-DO6lqmN_.js","assets/TestHistoryView-Bw260RVU.css","assets/TestListView-nUrNDqvm.js","assets/TestListView-Daa2nv3W.css","assets/CognitiveAssessmentView-D5kvBd1Z.js","assets/GlobalTimer-BPkfVOeW.js","assets/GlobalTimer-CFP44lZm.css","assets/CognitiveAssessmentView-Cs7t29rq.css","assets/PDQ5InstructionsView-xWDbLr1e.js","assets/speech-BAaOvQQ9.js","assets/PDQ5InstructionsView-Bo28ajMU.css","assets/PDQ5TestView-DAt79azD.js","assets/PDQ5TestView-CkZi_XPT.css","assets/MaskedEmotionTestView-BXiev3xG.js","assets/MaskedEmotionTestView-AuYGZ2ZS.css"])))=>i.map(i=>d[i]);
(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(r){if(r.ep)return;r.ep=!0;const o=n(r);fetch(r.href,o)}})();/**
* @vue/shared v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Ws(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const oe={},Bt=[],ze=()=>{},vl=()=>!1,Jn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Zs=e=>e.startsWith("onUpdate:"),de=Object.assign,Gs=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},bl=Object.prototype.hasOwnProperty,te=(e,t)=>bl.call(e,t),F=Array.isArray,Ut=e=>wn(e)==="[object Map]",Qt=e=>wn(e)==="[object Set]",br=e=>wn(e)==="[object Date]",q=e=>typeof e=="function",ae=e=>typeof e=="string",Qe=e=>typeof e=="symbol",ie=e=>e!==null&&typeof e=="object",Oo=e=>(ie(e)||q(e))&&q(e.then)&&q(e.catch),Mo=Object.prototype.toString,wn=e=>Mo.call(e),Tl=e=>wn(e).slice(8,-1),ko=e=>wn(e)==="[object Object]",zs=e=>ae(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,sn=Ws(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Yn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Sl=/-(\w)/g,Le=Yn(e=>e.replace(Sl,(t,n)=>n?n.toUpperCase():"")),El=/\B([A-Z])/g,St=Yn(e=>e.replace(El,"-$1").toLowerCase()),Xn=Yn(e=>e.charAt(0).toUpperCase()+e.slice(1)),hs=Yn(e=>e?`on${Xn(e)}`:""),_t=(e,t)=>!Object.is(e,t),Ln=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Ps=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},Vn=e=>{const t=parseFloat(e);return isNaN(t)?e:t},wl=e=>{const t=ae(e)?Number(e):NaN;return isNaN(t)?e:t};let Tr;const es=()=>Tr||(Tr=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Qs(e){if(F(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=ae(s)?Rl(s):Qs(s);if(r)for(const o in r)t[o]=r[o]}return t}else if(ae(e)||ie(e))return e}const Cl=/;(?![^(]*\))/g,xl=/:([^]+)/,Al=/\/\*[^]*?\*\//g;function Rl(e){const t={};return e.replace(Al,"").split(Cl).forEach(n=>{if(n){const s=n.split(xl);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Kt(e){let t="";if(ae(e))t=e;else if(F(e))for(let n=0;n<e.length;n++){const s=Kt(e[n]);s&&(t+=s+" ")}else if(ie(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Pl="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Ol=Ws(Pl);function Io(e){return!!e||e===""}function Ml(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=kt(e[s],t[s]);return n}function kt(e,t){if(e===t)return!0;let n=br(e),s=br(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=Qe(e),s=Qe(t),n||s)return e===t;if(n=F(e),s=F(t),n||s)return n&&s?Ml(e,t):!1;if(n=ie(e),s=ie(t),n||s){if(!n||!s)return!1;const r=Object.keys(e).length,o=Object.keys(t).length;if(r!==o)return!1;for(const i in e){const l=e.hasOwnProperty(i),c=t.hasOwnProperty(i);if(l&&!c||!l&&c||!kt(e[i],t[i]))return!1}}return String(e)===String(t)}function Js(e,t){return e.findIndex(n=>kt(n,t))}const Do=e=>!!(e&&e.__v_isRef===!0),Lo=e=>ae(e)?e:e==null?"":F(e)||ie(e)&&(e.toString===Mo||!q(e.toString))?Do(e)?Lo(e.value):JSON.stringify(e,No,2):String(e),No=(e,t)=>Do(t)?No(e,t.value):Ut(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],o)=>(n[ps(s,o)+" =>"]=r,n),{})}:Qt(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>ps(n))}:Qe(t)?ps(t):ie(t)&&!F(t)&&!ko(t)?String(t):t,ps=(e,t="")=>{var n;return Qe(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let pe;class Fo{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=pe,!t&&pe&&(this.index=(pe.scopes||(pe.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=pe;try{return pe=this,t()}finally{pe=n}}}on(){++this._on===1&&(this.prevScope=pe,pe=this)}off(){this._on>0&&--this._on===0&&(pe=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function Ho(e){return new Fo(e)}function $o(){return pe}function kl(e,t=!1){pe&&pe.cleanups.push(e)}let ce;const gs=new WeakSet;class jo{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,pe&&pe.active&&pe.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,gs.has(this)&&(gs.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Bo(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Sr(this),Uo(this);const t=ce,n=Fe;ce=this,Fe=!0;try{return this.fn()}finally{Ko(this),ce=t,Fe=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)er(t);this.deps=this.depsTail=void 0,Sr(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?gs.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Os(this)&&this.run()}get dirty(){return Os(this)}}let Vo=0,rn,on;function Bo(e,t=!1){if(e.flags|=8,t){e.next=on,on=e;return}e.next=rn,rn=e}function Ys(){Vo++}function Xs(){if(--Vo>0)return;if(on){let t=on;for(on=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;rn;){let t=rn;for(rn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function Uo(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Ko(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),er(s),Il(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function Os(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(qo(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function qo(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===gn)||(e.globalVersion=gn,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Os(e))))return;e.flags|=2;const t=e.dep,n=ce,s=Fe;ce=e,Fe=!0;try{Uo(e);const r=e.fn(e._value);(t.version===0||_t(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{ce=n,Fe=s,Ko(e),e.flags&=-3}}function er(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)er(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Il(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Fe=!0;const Wo=[];function ot(){Wo.push(Fe),Fe=!1}function it(){const e=Wo.pop();Fe=e===void 0?!0:e}function Sr(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=ce;ce=void 0;try{t()}finally{ce=n}}}let gn=0;class Dl{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class tr{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!ce||!Fe||ce===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==ce)n=this.activeLink=new Dl(ce,this),ce.deps?(n.prevDep=ce.depsTail,ce.depsTail.nextDep=n,ce.depsTail=n):ce.deps=ce.depsTail=n,Zo(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=ce.depsTail,n.nextDep=void 0,ce.depsTail.nextDep=n,ce.depsTail=n,ce.deps===n&&(ce.deps=s)}return n}trigger(t){this.version++,gn++,this.notify(t)}notify(t){Ys();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Xs()}}}function Zo(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)Zo(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Bn=new WeakMap,Ot=Symbol(""),Ms=Symbol(""),mn=Symbol("");function ge(e,t,n){if(Fe&&ce){let s=Bn.get(e);s||Bn.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new tr),r.map=s,r.key=n),r.track()}}function nt(e,t,n,s,r,o){const i=Bn.get(e);if(!i){gn++;return}const l=c=>{c&&c.trigger()};if(Ys(),t==="clear")i.forEach(l);else{const c=F(e),u=c&&zs(n);if(c&&n==="length"){const a=Number(s);i.forEach((f,p)=>{(p==="length"||p===mn||!Qe(p)&&p>=a)&&l(f)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),u&&l(i.get(mn)),t){case"add":c?u&&l(i.get("length")):(l(i.get(Ot)),Ut(e)&&l(i.get(Ms)));break;case"delete":c||(l(i.get(Ot)),Ut(e)&&l(i.get(Ms)));break;case"set":Ut(e)&&l(i.get(Ot));break}}Xs()}function Ll(e,t){const n=Bn.get(e);return n&&n.get(t)}function Nt(e){const t=Y(e);return t===e?t:(ge(t,"iterate",mn),ke(e)?t:t.map(he))}function ts(e){return ge(e=Y(e),"iterate",mn),e}const Nl={__proto__:null,[Symbol.iterator](){return ms(this,Symbol.iterator,he)},concat(...e){return Nt(this).concat(...e.map(t=>F(t)?Nt(t):t))},entries(){return ms(this,"entries",e=>(e[1]=he(e[1]),e))},every(e,t){return Ye(this,"every",e,t,void 0,arguments)},filter(e,t){return Ye(this,"filter",e,t,n=>n.map(he),arguments)},find(e,t){return Ye(this,"find",e,t,he,arguments)},findIndex(e,t){return Ye(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Ye(this,"findLast",e,t,he,arguments)},findLastIndex(e,t){return Ye(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Ye(this,"forEach",e,t,void 0,arguments)},includes(...e){return _s(this,"includes",e)},indexOf(...e){return _s(this,"indexOf",e)},join(e){return Nt(this).join(e)},lastIndexOf(...e){return _s(this,"lastIndexOf",e)},map(e,t){return Ye(this,"map",e,t,void 0,arguments)},pop(){return Yt(this,"pop")},push(...e){return Yt(this,"push",e)},reduce(e,...t){return Er(this,"reduce",e,t)},reduceRight(e,...t){return Er(this,"reduceRight",e,t)},shift(){return Yt(this,"shift")},some(e,t){return Ye(this,"some",e,t,void 0,arguments)},splice(...e){return Yt(this,"splice",e)},toReversed(){return Nt(this).toReversed()},toSorted(e){return Nt(this).toSorted(e)},toSpliced(...e){return Nt(this).toSpliced(...e)},unshift(...e){return Yt(this,"unshift",e)},values(){return ms(this,"values",he)}};function ms(e,t,n){const s=ts(e),r=s[t]();return s!==e&&!ke(e)&&(r._next=r.next,r.next=()=>{const o=r._next();return o.value&&(o.value=n(o.value)),o}),r}const Fl=Array.prototype;function Ye(e,t,n,s,r,o){const i=ts(e),l=i!==e&&!ke(e),c=i[t];if(c!==Fl[t]){const f=c.apply(e,o);return l?he(f):f}let u=n;i!==e&&(l?u=function(f,p){return n.call(this,he(f),p,e)}:n.length>2&&(u=function(f,p){return n.call(this,f,p,e)}));const a=c.call(i,u,s);return l&&r?r(a):a}function Er(e,t,n,s){const r=ts(e);let o=n;return r!==e&&(ke(e)?n.length>3&&(o=function(i,l,c){return n.call(this,i,l,c,e)}):o=function(i,l,c){return n.call(this,i,he(l),c,e)}),r[t](o,...s)}function _s(e,t,n){const s=Y(e);ge(s,"iterate",mn);const r=s[t](...n);return(r===-1||r===!1)&&rr(n[0])?(n[0]=Y(n[0]),s[t](...n)):r}function Yt(e,t,n=[]){ot(),Ys();const s=Y(e)[t].apply(e,n);return Xs(),it(),s}const Hl=Ws("__proto__,__v_isRef,__isVue"),Go=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Qe));function $l(e){Qe(e)||(e=String(e));const t=Y(this);return ge(t,"has",e),t.hasOwnProperty(e)}class zo{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return o;if(n==="__v_raw")return s===(r?o?zl:Xo:o?Yo:Jo).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=F(t);if(!r){let c;if(i&&(c=Nl[n]))return c;if(n==="hasOwnProperty")return $l}const l=Reflect.get(t,n,ue(t)?t:s);return(Qe(n)?Go.has(n):Hl(n))||(r||ge(t,"get",n),o)?l:ue(l)?i&&zs(n)?l:l.value:ie(l)?r?ti(l):Cn(l):l}}class Qo extends zo{constructor(t=!1){super(!1,t)}set(t,n,s,r){let o=t[n];if(!this._isShallow){const c=vt(o);if(!ke(s)&&!vt(s)&&(o=Y(o),s=Y(s)),!F(t)&&ue(o)&&!ue(s))return c?!1:(o.value=s,!0)}const i=F(t)&&zs(n)?Number(n)<t.length:te(t,n),l=Reflect.set(t,n,s,ue(t)?t:r);return t===Y(r)&&(i?_t(s,o)&&nt(t,"set",n,s):nt(t,"add",n,s)),l}deleteProperty(t,n){const s=te(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&nt(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!Qe(n)||!Go.has(n))&&ge(t,"has",n),s}ownKeys(t){return ge(t,"iterate",F(t)?"length":Ot),Reflect.ownKeys(t)}}class jl extends zo{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Vl=new Qo,Bl=new jl,Ul=new Qo(!0);const ks=e=>e,Pn=e=>Reflect.getPrototypeOf(e);function Kl(e,t,n){return function(...s){const r=this.__v_raw,o=Y(r),i=Ut(o),l=e==="entries"||e===Symbol.iterator&&i,c=e==="keys"&&i,u=r[e](...s),a=n?ks:t?Un:he;return!t&&ge(o,"iterate",c?Ms:Ot),{next(){const{value:f,done:p}=u.next();return p?{value:f,done:p}:{value:l?[a(f[0]),a(f[1])]:a(f),done:p}},[Symbol.iterator](){return this}}}}function On(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function ql(e,t){const n={get(r){const o=this.__v_raw,i=Y(o),l=Y(r);e||(_t(r,l)&&ge(i,"get",r),ge(i,"get",l));const{has:c}=Pn(i),u=t?ks:e?Un:he;if(c.call(i,r))return u(o.get(r));if(c.call(i,l))return u(o.get(l));o!==i&&o.get(r)},get size(){const r=this.__v_raw;return!e&&ge(Y(r),"iterate",Ot),Reflect.get(r,"size",r)},has(r){const o=this.__v_raw,i=Y(o),l=Y(r);return e||(_t(r,l)&&ge(i,"has",r),ge(i,"has",l)),r===l?o.has(r):o.has(r)||o.has(l)},forEach(r,o){const i=this,l=i.__v_raw,c=Y(l),u=t?ks:e?Un:he;return!e&&ge(c,"iterate",Ot),l.forEach((a,f)=>r.call(o,u(a),u(f),i))}};return de(n,e?{add:On("add"),set:On("set"),delete:On("delete"),clear:On("clear")}:{add(r){!t&&!ke(r)&&!vt(r)&&(r=Y(r));const o=Y(this);return Pn(o).has.call(o,r)||(o.add(r),nt(o,"add",r,r)),this},set(r,o){!t&&!ke(o)&&!vt(o)&&(o=Y(o));const i=Y(this),{has:l,get:c}=Pn(i);let u=l.call(i,r);u||(r=Y(r),u=l.call(i,r));const a=c.call(i,r);return i.set(r,o),u?_t(o,a)&&nt(i,"set",r,o):nt(i,"add",r,o),this},delete(r){const o=Y(this),{has:i,get:l}=Pn(o);let c=i.call(o,r);c||(r=Y(r),c=i.call(o,r)),l&&l.call(o,r);const u=o.delete(r);return c&&nt(o,"delete",r,void 0),u},clear(){const r=Y(this),o=r.size!==0,i=r.clear();return o&&nt(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=Kl(r,e,t)}),n}function nr(e,t){const n=ql(e,t);return(s,r,o)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(te(n,r)&&r in s?n:s,r,o)}const Wl={get:nr(!1,!1)},Zl={get:nr(!1,!0)},Gl={get:nr(!0,!1)};const Jo=new WeakMap,Yo=new WeakMap,Xo=new WeakMap,zl=new WeakMap;function Ql(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Jl(e){return e.__v_skip||!Object.isExtensible(e)?0:Ql(Tl(e))}function Cn(e){return vt(e)?e:sr(e,!1,Vl,Wl,Jo)}function ei(e){return sr(e,!1,Ul,Zl,Yo)}function ti(e){return sr(e,!0,Bl,Gl,Xo)}function sr(e,t,n,s,r){if(!ie(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=Jl(e);if(o===0)return e;const i=r.get(e);if(i)return i;const l=new Proxy(e,o===2?s:n);return r.set(e,l),l}function yt(e){return vt(e)?yt(e.__v_raw):!!(e&&e.__v_isReactive)}function vt(e){return!!(e&&e.__v_isReadonly)}function ke(e){return!!(e&&e.__v_isShallow)}function rr(e){return e?!!e.__v_raw:!1}function Y(e){const t=e&&e.__v_raw;return t?Y(t):e}function or(e){return!te(e,"__v_skip")&&Object.isExtensible(e)&&Ps(e,"__v_skip",!0),e}const he=e=>ie(e)?Cn(e):e,Un=e=>ie(e)?ti(e):e;function ue(e){return e?e.__v_isRef===!0:!1}function It(e){return ni(e,!1)}function Yl(e){return ni(e,!0)}function ni(e,t){return ue(e)?e:new Xl(e,t)}class Xl{constructor(t,n){this.dep=new tr,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:Y(t),this._value=n?t:he(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||ke(t)||vt(t);t=s?t:Y(t),_t(t,n)&&(this._rawValue=t,this._value=s?t:he(t),this.dep.trigger())}}function Ne(e){return ue(e)?e.value:e}const ec={get:(e,t,n)=>t==="__v_raw"?e:Ne(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return ue(r)&&!ue(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function si(e){return yt(e)?e:new Proxy(e,ec)}function tc(e){const t=F(e)?new Array(e.length):{};for(const n in e)t[n]=sc(e,n);return t}class nc{constructor(t,n,s){this._object=t,this._key=n,this._defaultValue=s,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return Ll(Y(this._object),this._key)}}function sc(e,t,n){const s=e[t];return ue(s)?s:new nc(e,t,n)}class rc{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new tr(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=gn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&ce!==this)return Bo(this,!0),!0}get value(){const t=this.dep.track();return qo(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function oc(e,t,n=!1){let s,r;return q(e)?s=e:(s=e.get,r=e.set),new rc(s,r,n)}const Mn={},Kn=new WeakMap;let At;function ic(e,t=!1,n=At){if(n){let s=Kn.get(n);s||Kn.set(n,s=[]),s.push(e)}}function lc(e,t,n=oe){const{immediate:s,deep:r,once:o,scheduler:i,augmentJob:l,call:c}=n,u=M=>r?M:ke(M)||r===!1||r===0?st(M,1):st(M);let a,f,p,g,T=!1,E=!1;if(ue(e)?(f=()=>e.value,T=ke(e)):yt(e)?(f=()=>u(e),T=!0):F(e)?(E=!0,T=e.some(M=>yt(M)||ke(M)),f=()=>e.map(M=>{if(ue(M))return M.value;if(yt(M))return u(M);if(q(M))return c?c(M,2):M()})):q(e)?t?f=c?()=>c(e,2):e:f=()=>{if(p){ot();try{p()}finally{it()}}const M=At;At=a;try{return c?c(e,3,[g]):e(g)}finally{At=M}}:f=ze,t&&r){const M=f,j=r===!0?1/0:r;f=()=>st(M(),j)}const U=$o(),D=()=>{a.stop(),U&&U.active&&Gs(U.effects,a)};if(o&&t){const M=t;t=(...j)=>{M(...j),D()}}let I=E?new Array(e.length).fill(Mn):Mn;const L=M=>{if(!(!(a.flags&1)||!a.dirty&&!M))if(t){const j=a.run();if(r||T||(E?j.some((G,Z)=>_t(G,I[Z])):_t(j,I))){p&&p();const G=At;At=a;try{const Z=[j,I===Mn?void 0:E&&I[0]===Mn?[]:I,g];I=j,c?c(t,3,Z):t(...Z)}finally{At=G}}}else a.run()};return l&&l(L),a=new jo(f),a.scheduler=i?()=>i(L,!1):L,g=M=>ic(M,!1,a),p=a.onStop=()=>{const M=Kn.get(a);if(M){if(c)c(M,4);else for(const j of M)j();Kn.delete(a)}},t?s?L(!0):I=a.run():i?i(L.bind(null,!0),!0):a.run(),D.pause=a.pause.bind(a),D.resume=a.resume.bind(a),D.stop=D,D}function st(e,t=1/0,n){if(t<=0||!ie(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,ue(e))st(e.value,t,n);else if(F(e))for(let s=0;s<e.length;s++)st(e[s],t,n);else if(Qt(e)||Ut(e))e.forEach(s=>{st(s,t,n)});else if(ko(e)){for(const s in e)st(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&st(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function xn(e,t,n,s){try{return s?e(...s):e()}catch(r){ns(r,t,n)}}function He(e,t,n,s){if(q(e)){const r=xn(e,t,n,s);return r&&Oo(r)&&r.catch(o=>{ns(o,t,n)}),r}if(F(e)){const r=[];for(let o=0;o<e.length;o++)r.push(He(e[o],t,n,s));return r}}function ns(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||oe;if(t){let l=t.parent;const c=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const a=l.ec;if(a){for(let f=0;f<a.length;f++)if(a[f](e,c,u)===!1)return}l=l.parent}if(o){ot(),xn(o,null,10,[e,c,u]),it();return}}cc(e,n,r,s,i)}function cc(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const Se=[];let We=-1;const qt=[];let ht=null,Ht=0;const ri=Promise.resolve();let qn=null;function ss(e){const t=qn||ri;return e?t.then(this?e.bind(this):e):t}function ac(e){let t=We+1,n=Se.length;for(;t<n;){const s=t+n>>>1,r=Se[s],o=_n(r);o<e||o===e&&r.flags&2?t=s+1:n=s}return t}function ir(e){if(!(e.flags&1)){const t=_n(e),n=Se[Se.length-1];!n||!(e.flags&2)&&t>=_n(n)?Se.push(e):Se.splice(ac(t),0,e),e.flags|=1,oi()}}function oi(){qn||(qn=ri.then(li))}function uc(e){F(e)?qt.push(...e):ht&&e.id===-1?ht.splice(Ht+1,0,e):e.flags&1||(qt.push(e),e.flags|=1),oi()}function wr(e,t,n=We+1){for(;n<Se.length;n++){const s=Se[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;Se.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function ii(e){if(qt.length){const t=[...new Set(qt)].sort((n,s)=>_n(n)-_n(s));if(qt.length=0,ht){ht.push(...t);return}for(ht=t,Ht=0;Ht<ht.length;Ht++){const n=ht[Ht];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}ht=null,Ht=0}}const _n=e=>e.id==null?e.flags&2?-1:1/0:e.id;function li(e){try{for(We=0;We<Se.length;We++){const t=Se[We];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),xn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;We<Se.length;We++){const t=Se[We];t&&(t.flags&=-2)}We=-1,Se.length=0,ii(),qn=null,(Se.length||qt.length)&&li()}}let Re=null,ci=null;function Wn(e){const t=Re;return Re=e,ci=e&&e.type.__scopeId||null,t}function fc(e,t=Re,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&Hr(-1);const o=Wn(t);let i;try{i=e(...r)}finally{Wn(o),s._d&&Hr(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function xf(e,t){if(Re===null)return e;const n=as(Re),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[o,i,l,c=oe]=t[r];o&&(q(o)&&(o={mounted:o,updated:o}),o.deep&&st(i),s.push({dir:o,instance:n,value:i,oldValue:void 0,arg:l,modifiers:c}))}return e}function Et(e,t,n,s){const r=e.dirs,o=t&&t.dirs;for(let i=0;i<r.length;i++){const l=r[i];o&&(l.oldValue=o[i].value);let c=l.dir[s];c&&(ot(),He(c,n,8,[e.el,l,e,t]),it())}}const ai=Symbol("_vte"),ui=e=>e.__isTeleport,ln=e=>e&&(e.disabled||e.disabled===""),Cr=e=>e&&(e.defer||e.defer===""),xr=e=>typeof SVGElement<"u"&&e instanceof SVGElement,Ar=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,Is=(e,t)=>{const n=e&&e.to;return ae(n)?t?t(n):null:n},fi={name:"Teleport",__isTeleport:!0,process(e,t,n,s,r,o,i,l,c,u){const{mc:a,pc:f,pbc:p,o:{insert:g,querySelector:T,createText:E,createComment:U}}=u,D=ln(t.props);let{shapeFlag:I,children:L,dynamicChildren:M}=t;if(e==null){const j=t.el=E(""),G=t.anchor=E("");g(j,n,s),g(G,n,s);const Z=(w,B)=>{I&16&&(r&&r.isCE&&(r.ce._teleportTarget=w),a(L,w,B,r,o,i,l,c))},V=()=>{const w=t.target=Is(t.props,T),B=di(w,t,E,g);w&&(i!=="svg"&&xr(w)?i="svg":i!=="mathml"&&Ar(w)&&(i="mathml"),D||(Z(w,B),Nn(t,!1)))};D&&(Z(n,G),Nn(t,!0)),Cr(t.props)?(t.el.__isMounted=!1,Te(()=>{V(),delete t.el.__isMounted},o)):V()}else{if(Cr(t.props)&&e.el.__isMounted===!1){Te(()=>{fi.process(e,t,n,s,r,o,i,l,c,u)},o);return}t.el=e.el,t.targetStart=e.targetStart;const j=t.anchor=e.anchor,G=t.target=e.target,Z=t.targetAnchor=e.targetAnchor,V=ln(e.props),w=V?n:G,B=V?j:Z;if(i==="svg"||xr(G)?i="svg":(i==="mathml"||Ar(G))&&(i="mathml"),M?(p(e.dynamicChildren,M,w,r,o,i,l),ur(e,t,!0)):c||f(e,t,w,B,r,o,i,l,!1),D)V?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):kn(t,n,j,u,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const z=t.target=Is(t.props,T);z&&kn(t,z,null,u,0)}else V&&kn(t,G,Z,u,1);Nn(t,D)}},remove(e,t,n,{um:s,o:{remove:r}},o){const{shapeFlag:i,children:l,anchor:c,targetStart:u,targetAnchor:a,target:f,props:p}=e;if(f&&(r(u),r(a)),o&&r(c),i&16){const g=o||!ln(p);for(let T=0;T<l.length;T++){const E=l[T];s(E,t,n,g,!!E.dynamicChildren)}}},move:kn,hydrate:dc};function kn(e,t,n,{o:{insert:s},m:r},o=2){o===0&&s(e.targetAnchor,t,n);const{el:i,anchor:l,shapeFlag:c,children:u,props:a}=e,f=o===2;if(f&&s(i,t,n),(!f||ln(a))&&c&16)for(let p=0;p<u.length;p++)r(u[p],t,n,2);f&&s(l,t,n)}function dc(e,t,n,s,r,o,{o:{nextSibling:i,parentNode:l,querySelector:c,insert:u,createText:a}},f){const p=t.target=Is(t.props,c);if(p){const g=ln(t.props),T=p._lpa||p.firstChild;if(t.shapeFlag&16)if(g)t.anchor=f(i(e),t,l(e),n,s,r,o),t.targetStart=T,t.targetAnchor=T&&i(T);else{t.anchor=i(e);let E=T;for(;E;){if(E&&E.nodeType===8){if(E.data==="teleport start anchor")t.targetStart=E;else if(E.data==="teleport anchor"){t.targetAnchor=E,p._lpa=t.targetAnchor&&i(t.targetAnchor);break}}E=i(E)}t.targetAnchor||di(p,t,a,u),f(T&&i(T),t,p,n,s,r,o)}Nn(t,g)}return t.anchor&&i(t.anchor)}const Af=fi;function Nn(e,t){const n=e.ctx;if(n&&n.ut){let s,r;for(t?(s=e.el,r=e.anchor):(s=e.targetStart,r=e.targetAnchor);s&&s!==r;)s.nodeType===1&&s.setAttribute("data-v-owner",n.uid),s=s.nextSibling;n.ut()}}function di(e,t,n,s){const r=t.targetStart=n(""),o=t.targetAnchor=n("");return r[ai]=o,e&&(s(r,e),s(o,e)),o}const pt=Symbol("_leaveCb"),In=Symbol("_enterCb");function hc(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return lr(()=>{e.isMounted=!0}),bi(()=>{e.isUnmounting=!0}),e}const Me=[Function,Array],hi={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Me,onEnter:Me,onAfterEnter:Me,onEnterCancelled:Me,onBeforeLeave:Me,onLeave:Me,onAfterLeave:Me,onLeaveCancelled:Me,onBeforeAppear:Me,onAppear:Me,onAfterAppear:Me,onAppearCancelled:Me},pi=e=>{const t=e.subTree;return t.component?pi(t.component):t},pc={name:"BaseTransition",props:hi,setup(e,{slots:t}){const n=dr(),s=hc();return()=>{const r=t.default&&_i(t.default(),!0);if(!r||!r.length)return;const o=gi(r),i=Y(e),{mode:l}=i;if(s.isLeaving)return ys(o);const c=Rr(o);if(!c)return ys(o);let u=Ds(c,i,s,n,f=>u=f);c.type!==Ee&&yn(c,u);let a=n.subTree&&Rr(n.subTree);if(a&&a.type!==Ee&&!Rt(c,a)&&pi(n).type!==Ee){let f=Ds(a,i,s,n);if(yn(a,f),l==="out-in"&&c.type!==Ee)return s.isLeaving=!0,f.afterLeave=()=>{s.isLeaving=!1,n.job.flags&8||n.update(),delete f.afterLeave,a=void 0},ys(o);l==="in-out"&&c.type!==Ee?f.delayLeave=(p,g,T)=>{const E=mi(s,a);E[String(a.key)]=a,p[pt]=()=>{g(),p[pt]=void 0,delete u.delayedLeave,a=void 0},u.delayedLeave=()=>{T(),delete u.delayedLeave,a=void 0}}:a=void 0}else a&&(a=void 0);return o}}};function gi(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==Ee){t=n;break}}return t}const gc=pc;function mi(e,t){const{leavingVNodes:n}=e;let s=n.get(t.type);return s||(s=Object.create(null),n.set(t.type,s)),s}function Ds(e,t,n,s,r){const{appear:o,mode:i,persisted:l=!1,onBeforeEnter:c,onEnter:u,onAfterEnter:a,onEnterCancelled:f,onBeforeLeave:p,onLeave:g,onAfterLeave:T,onLeaveCancelled:E,onBeforeAppear:U,onAppear:D,onAfterAppear:I,onAppearCancelled:L}=t,M=String(e.key),j=mi(n,e),G=(w,B)=>{w&&He(w,s,9,B)},Z=(w,B)=>{const z=B[1];G(w,B),F(w)?w.every(O=>O.length<=1)&&z():w.length<=1&&z()},V={mode:i,persisted:l,beforeEnter(w){let B=c;if(!n.isMounted)if(o)B=U||c;else return;w[pt]&&w[pt](!0);const z=j[M];z&&Rt(e,z)&&z.el[pt]&&z.el[pt](),G(B,[w])},enter(w){let B=u,z=a,O=f;if(!n.isMounted)if(o)B=D||u,z=I||a,O=L||f;else return;let Q=!1;const fe=w[In]=ve=>{Q||(Q=!0,ve?G(O,[w]):G(z,[w]),V.delayedLeave&&V.delayedLeave(),w[In]=void 0)};B?Z(B,[w,fe]):fe()},leave(w,B){const z=String(e.key);if(w[In]&&w[In](!0),n.isUnmounting)return B();G(p,[w]);let O=!1;const Q=w[pt]=fe=>{O||(O=!0,B(),fe?G(E,[w]):G(T,[w]),w[pt]=void 0,j[z]===e&&delete j[z])};j[z]=e,g?Z(g,[w,Q]):Q()},clone(w){const B=Ds(w,t,n,s,r);return r&&r(B),B}};return V}function ys(e){if(os(e))return e=bt(e),e.children=null,e}function Rr(e){if(!os(e))return ui(e.type)&&e.children?gi(e.children):e;if(e.component)return e.component.subTree;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&q(n.default))return n.default()}}function yn(e,t){e.shapeFlag&6&&e.component?(e.transition=t,yn(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function _i(e,t=!1,n){let s=[],r=0;for(let o=0;o<e.length;o++){let i=e[o];const l=n==null?i.key:String(n)+String(i.key!=null?i.key:o);i.type===Ze?(i.patchFlag&128&&r++,s=s.concat(_i(i.children,t,l))):(t||i.type!==Ee)&&s.push(l!=null?bt(i,{key:l}):i)}if(r>1)for(let o=0;o<s.length;o++)s[o].patchFlag=-2;return s}/*! #__NO_SIDE_EFFECTS__ */function rs(e,t){return q(e)?de({name:e.name},t,{setup:e}):e}function yi(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function cn(e,t,n,s,r=!1){if(F(e)){e.forEach((T,E)=>cn(T,t&&(F(t)?t[E]:t),n,s,r));return}if(an(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&cn(e,t,n,s.component.subTree);return}const o=s.shapeFlag&4?as(s.component):s.el,i=r?null:o,{i:l,r:c}=e,u=t&&t.r,a=l.refs===oe?l.refs={}:l.refs,f=l.setupState,p=Y(f),g=f===oe?()=>!1:T=>te(p,T);if(u!=null&&u!==c&&(ae(u)?(a[u]=null,g(u)&&(f[u]=null)):ue(u)&&(u.value=null)),q(c))xn(c,l,12,[i,a]);else{const T=ae(c),E=ue(c);if(T||E){const U=()=>{if(e.f){const D=T?g(c)?f[c]:a[c]:c.value;r?F(D)&&Gs(D,o):F(D)?D.includes(o)||D.push(o):T?(a[c]=[o],g(c)&&(f[c]=a[c])):(c.value=[o],e.k&&(a[e.k]=c.value))}else T?(a[c]=i,g(c)&&(f[c]=i)):E&&(c.value=i,e.k&&(a[e.k]=i))};i?(U.id=-1,Te(U,n)):U()}}}es().requestIdleCallback;es().cancelIdleCallback;const an=e=>!!e.type.__asyncLoader,os=e=>e.type.__isKeepAlive;function mc(e,t){vi(e,"a",t)}function _c(e,t){vi(e,"da",t)}function vi(e,t,n=me){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(is(t,s,n),n){let r=n.parent;for(;r&&r.parent;)os(r.parent.vnode)&&yc(s,t,n,r),r=r.parent}}function yc(e,t,n,s){const r=is(t,e,s,!0);Ti(()=>{Gs(s[t],r)},n)}function is(e,t,n=me,s=!1){if(n){const r=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{ot();const l=An(n),c=He(t,n,e,i);return l(),it(),c});return s?r.unshift(o):r.push(o),o}}const lt=e=>(t,n=me)=>{(!bn||e==="sp")&&is(e,(...s)=>t(...s),n)},vc=lt("bm"),lr=lt("m"),bc=lt("bu"),Tc=lt("u"),bi=lt("bum"),Ti=lt("um"),Sc=lt("sp"),Ec=lt("rtg"),wc=lt("rtc");function Cc(e,t=me){is("ec",e,t)}const Si="components";function xc(e,t){return wi(Si,e,!0,t)||e}const Ei=Symbol.for("v-ndc");function Rf(e){return ae(e)?wi(Si,e,!1)||e:e||Ei}function wi(e,t,n=!0,s=!1){const r=Re||me;if(r){const o=r.type;{const l=ga(o,!1);if(l&&(l===t||l===Le(t)||l===Xn(Le(t))))return o}const i=Pr(r[e]||o[e],t)||Pr(r.appContext[e],t);return!i&&s?o:i}}function Pr(e,t){return e&&(e[t]||e[Le(t)]||e[Xn(Le(t))])}function Pf(e,t,n,s){let r;const o=n,i=F(e);if(i||ae(e)){const l=i&&yt(e);let c=!1,u=!1;l&&(c=!ke(e),u=vt(e),e=ts(e)),r=new Array(e.length);for(let a=0,f=e.length;a<f;a++)r[a]=t(c?u?Un(he(e[a])):he(e[a]):e[a],a,void 0,o)}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,o)}else if(ie(e))if(e[Symbol.iterator])r=Array.from(e,(l,c)=>t(l,c,void 0,o));else{const l=Object.keys(e);r=new Array(l.length);for(let c=0,u=l.length;c<u;c++){const a=l[c];r[c]=t(e[a],a,c,o)}}else r=[];return r}const Ls=e=>e?Ki(e)?as(e):Ls(e.parent):null,un=de(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ls(e.parent),$root:e=>Ls(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>xi(e),$forceUpdate:e=>e.f||(e.f=()=>{ir(e.update)}),$nextTick:e=>e.n||(e.n=ss.bind(e.proxy)),$watch:e=>Gc.bind(e)}),vs=(e,t)=>e!==oe&&!e.__isScriptSetup&&te(e,t),Ac={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:o,accessCache:i,type:l,appContext:c}=e;let u;if(t[0]!=="$"){const g=i[t];if(g!==void 0)switch(g){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return o[t]}else{if(vs(s,t))return i[t]=1,s[t];if(r!==oe&&te(r,t))return i[t]=2,r[t];if((u=e.propsOptions[0])&&te(u,t))return i[t]=3,o[t];if(n!==oe&&te(n,t))return i[t]=4,n[t];Ns&&(i[t]=0)}}const a=un[t];let f,p;if(a)return t==="$attrs"&&ge(e.attrs,"get",""),a(e);if((f=l.__cssModules)&&(f=f[t]))return f;if(n!==oe&&te(n,t))return i[t]=4,n[t];if(p=c.config.globalProperties,te(p,t))return p[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:o}=e;return vs(r,t)?(r[t]=n,!0):s!==oe&&te(s,t)?(s[t]=n,!0):te(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:o}},i){let l;return!!n[i]||e!==oe&&te(e,i)||vs(t,i)||(l=o[0])&&te(l,i)||te(s,i)||te(un,i)||te(r.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:te(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Or(e){return F(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Ns=!0;function Rc(e){const t=xi(e),n=e.proxy,s=e.ctx;Ns=!1,t.beforeCreate&&Mr(t.beforeCreate,e,"bc");const{data:r,computed:o,methods:i,watch:l,provide:c,inject:u,created:a,beforeMount:f,mounted:p,beforeUpdate:g,updated:T,activated:E,deactivated:U,beforeDestroy:D,beforeUnmount:I,destroyed:L,unmounted:M,render:j,renderTracked:G,renderTriggered:Z,errorCaptured:V,serverPrefetch:w,expose:B,inheritAttrs:z,components:O,directives:Q,filters:fe}=t;if(u&&Pc(u,s,null),i)for(const W in i){const X=i[W];q(X)&&(s[W]=X.bind(n))}if(r){const W=r.call(n,n);ie(W)&&(e.data=Cn(W))}if(Ns=!0,o)for(const W in o){const X=o[W],Je=q(X)?X.bind(n,n):q(X.get)?X.get.bind(n,n):ze,ct=!q(X)&&q(X.set)?X.set.bind(n):ze,je=_e({get:Je,set:ct});Object.defineProperty(s,W,{enumerable:!0,configurable:!0,get:()=>je.value,set:we=>je.value=we})}if(l)for(const W in l)Ci(l[W],s,n,W);if(c){const W=q(c)?c.call(n):c;Reflect.ownKeys(W).forEach(X=>{Fn(X,W[X])})}a&&Mr(a,e,"c");function se(W,X){F(X)?X.forEach(Je=>W(Je.bind(n))):X&&W(X.bind(n))}if(se(vc,f),se(lr,p),se(bc,g),se(Tc,T),se(mc,E),se(_c,U),se(Cc,V),se(wc,G),se(Ec,Z),se(bi,I),se(Ti,M),se(Sc,w),F(B))if(B.length){const W=e.exposed||(e.exposed={});B.forEach(X=>{Object.defineProperty(W,X,{get:()=>n[X],set:Je=>n[X]=Je,enumerable:!0})})}else e.exposed||(e.exposed={});j&&e.render===ze&&(e.render=j),z!=null&&(e.inheritAttrs=z),O&&(e.components=O),Q&&(e.directives=Q),w&&yi(e)}function Pc(e,t,n=ze){F(e)&&(e=Fs(e));for(const s in e){const r=e[s];let o;ie(r)?"default"in r?o=Ie(r.from||s,r.default,!0):o=Ie(r.from||s):o=Ie(r),ue(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[s]=o}}function Mr(e,t,n){He(F(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Ci(e,t,n,s){let r=s.includes(".")?Hi(n,s):()=>n[s];if(ae(e)){const o=t[e];q(o)&&Wt(r,o)}else if(q(e))Wt(r,e.bind(n));else if(ie(e))if(F(e))e.forEach(o=>Ci(o,t,n,s));else{const o=q(e.handler)?e.handler.bind(n):t[e.handler];q(o)&&Wt(r,o,e)}}function xi(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,l=o.get(t);let c;return l?c=l:!r.length&&!n&&!s?c=t:(c={},r.length&&r.forEach(u=>Zn(c,u,i,!0)),Zn(c,t,i)),ie(t)&&o.set(t,c),c}function Zn(e,t,n,s=!1){const{mixins:r,extends:o}=t;o&&Zn(e,o,n,!0),r&&r.forEach(i=>Zn(e,i,n,!0));for(const i in t)if(!(s&&i==="expose")){const l=Oc[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const Oc={data:kr,props:Ir,emits:Ir,methods:nn,computed:nn,beforeCreate:be,created:be,beforeMount:be,mounted:be,beforeUpdate:be,updated:be,beforeDestroy:be,beforeUnmount:be,destroyed:be,unmounted:be,activated:be,deactivated:be,errorCaptured:be,serverPrefetch:be,components:nn,directives:nn,watch:kc,provide:kr,inject:Mc};function kr(e,t){return t?e?function(){return de(q(e)?e.call(this,this):e,q(t)?t.call(this,this):t)}:t:e}function Mc(e,t){return nn(Fs(e),Fs(t))}function Fs(e){if(F(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function be(e,t){return e?[...new Set([].concat(e,t))]:t}function nn(e,t){return e?de(Object.create(null),e,t):t}function Ir(e,t){return e?F(e)&&F(t)?[...new Set([...e,...t])]:de(Object.create(null),Or(e),Or(t??{})):t}function kc(e,t){if(!e)return t;if(!t)return e;const n=de(Object.create(null),e);for(const s in t)n[s]=be(e[s],t[s]);return n}function Ai(){return{app:null,config:{isNativeTag:vl,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Ic=0;function Dc(e,t){return function(s,r=null){q(s)||(s=de({},s)),r!=null&&!ie(r)&&(r=null);const o=Ai(),i=new WeakSet,l=[];let c=!1;const u=o.app={_uid:Ic++,_component:s,_props:r,_container:null,_context:o,_instance:null,version:_a,get config(){return o.config},set config(a){},use(a,...f){return i.has(a)||(a&&q(a.install)?(i.add(a),a.install(u,...f)):q(a)&&(i.add(a),a(u,...f))),u},mixin(a){return o.mixins.includes(a)||o.mixins.push(a),u},component(a,f){return f?(o.components[a]=f,u):o.components[a]},directive(a,f){return f?(o.directives[a]=f,u):o.directives[a]},mount(a,f,p){if(!c){const g=u._ceVNode||ye(s,r);return g.appContext=o,p===!0?p="svg":p===!1&&(p=void 0),e(g,a,p),c=!0,u._container=a,a.__vue_app__=u,as(g.component)}},onUnmount(a){l.push(a)},unmount(){c&&(He(l,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(a,f){return o.provides[a]=f,u},runWithContext(a){const f=Mt;Mt=u;try{return a()}finally{Mt=f}}};return u}}let Mt=null;function Fn(e,t){if(me){let n=me.provides;const s=me.parent&&me.parent.provides;s===n&&(n=me.provides=Object.create(s)),n[e]=t}}function Ie(e,t,n=!1){const s=dr();if(s||Mt){let r=Mt?Mt._context.provides:s?s.parent==null||s.ce?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&q(t)?t.call(s&&s.proxy):t}}function Lc(){return!!(dr()||Mt)}const Ri={},Pi=()=>Object.create(Ri),Oi=e=>Object.getPrototypeOf(e)===Ri;function Nc(e,t,n,s=!1){const r={},o=Pi();e.propsDefaults=Object.create(null),Mi(e,t,r,o);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=s?r:ei(r):e.type.props?e.props=r:e.props=o,e.attrs=o}function Fc(e,t,n,s){const{props:r,attrs:o,vnode:{patchFlag:i}}=e,l=Y(r),[c]=e.propsOptions;let u=!1;if((s||i>0)&&!(i&16)){if(i&8){const a=e.vnode.dynamicProps;for(let f=0;f<a.length;f++){let p=a[f];if(ls(e.emitsOptions,p))continue;const g=t[p];if(c)if(te(o,p))g!==o[p]&&(o[p]=g,u=!0);else{const T=Le(p);r[T]=Hs(c,l,T,g,e,!1)}else g!==o[p]&&(o[p]=g,u=!0)}}}else{Mi(e,t,r,o)&&(u=!0);let a;for(const f in l)(!t||!te(t,f)&&((a=St(f))===f||!te(t,a)))&&(c?n&&(n[f]!==void 0||n[a]!==void 0)&&(r[f]=Hs(c,l,f,void 0,e,!0)):delete r[f]);if(o!==l)for(const f in o)(!t||!te(t,f))&&(delete o[f],u=!0)}u&&nt(e.attrs,"set","")}function Mi(e,t,n,s){const[r,o]=e.propsOptions;let i=!1,l;if(t)for(let c in t){if(sn(c))continue;const u=t[c];let a;r&&te(r,a=Le(c))?!o||!o.includes(a)?n[a]=u:(l||(l={}))[a]=u:ls(e.emitsOptions,c)||(!(c in s)||u!==s[c])&&(s[c]=u,i=!0)}if(o){const c=Y(n),u=l||oe;for(let a=0;a<o.length;a++){const f=o[a];n[f]=Hs(r,c,f,u[f],e,!te(u,f))}}return i}function Hs(e,t,n,s,r,o){const i=e[n];if(i!=null){const l=te(i,"default");if(l&&s===void 0){const c=i.default;if(i.type!==Function&&!i.skipFactory&&q(c)){const{propsDefaults:u}=r;if(n in u)s=u[n];else{const a=An(r);s=u[n]=c.call(null,t),a()}}else s=c;r.ce&&r.ce._setProp(n,s)}i[0]&&(o&&!l?s=!1:i[1]&&(s===""||s===St(n))&&(s=!0))}return s}const Hc=new WeakMap;function ki(e,t,n=!1){const s=n?Hc:t.propsCache,r=s.get(e);if(r)return r;const o=e.props,i={},l=[];let c=!1;if(!q(e)){const a=f=>{c=!0;const[p,g]=ki(f,t,!0);de(i,p),g&&l.push(...g)};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}if(!o&&!c)return ie(e)&&s.set(e,Bt),Bt;if(F(o))for(let a=0;a<o.length;a++){const f=Le(o[a]);Dr(f)&&(i[f]=oe)}else if(o)for(const a in o){const f=Le(a);if(Dr(f)){const p=o[a],g=i[f]=F(p)||q(p)?{type:p}:de({},p),T=g.type;let E=!1,U=!0;if(F(T))for(let D=0;D<T.length;++D){const I=T[D],L=q(I)&&I.name;if(L==="Boolean"){E=!0;break}else L==="String"&&(U=!1)}else E=q(T)&&T.name==="Boolean";g[0]=E,g[1]=U,(E||te(g,"default"))&&l.push(f)}}const u=[i,l];return ie(e)&&s.set(e,u),u}function Dr(e){return e[0]!=="$"&&!sn(e)}const cr=e=>e==="_"||e==="__"||e==="_ctx"||e==="$stable",ar=e=>F(e)?e.map(Ge):[Ge(e)],$c=(e,t,n)=>{if(t._n)return t;const s=fc((...r)=>ar(t(...r)),n);return s._c=!1,s},Ii=(e,t,n)=>{const s=e._ctx;for(const r in e){if(cr(r))continue;const o=e[r];if(q(o))t[r]=$c(r,o,s);else if(o!=null){const i=ar(o);t[r]=()=>i}}},Di=(e,t)=>{const n=ar(t);e.slots.default=()=>n},Li=(e,t,n)=>{for(const s in t)(n||!cr(s))&&(e[s]=t[s])},jc=(e,t,n)=>{const s=e.slots=Pi();if(e.vnode.shapeFlag&32){const r=t.__;r&&Ps(s,"__",r,!0);const o=t._;o?(Li(s,t,n),n&&Ps(s,"_",o,!0)):Ii(t,s)}else t&&Di(e,t)},Vc=(e,t,n)=>{const{vnode:s,slots:r}=e;let o=!0,i=oe;if(s.shapeFlag&32){const l=t._;l?n&&l===1?o=!1:Li(r,t,n):(o=!t.$stable,Ii(t,r)),i=t}else t&&(Di(e,t),i={default:1});if(o)for(const l in r)!cr(l)&&i[l]==null&&delete r[l]},Te=ta;function Bc(e){return Uc(e)}function Uc(e,t){const n=es();n.__VUE__=!0;const{insert:s,remove:r,patchProp:o,createElement:i,createText:l,createComment:c,setText:u,setElementText:a,parentNode:f,nextSibling:p,setScopeId:g=ze,insertStaticContent:T}=e,E=(d,h,m,_=null,b=null,v=null,A=void 0,x=null,C=!!h.dynamicChildren)=>{if(d===h)return;d&&!Rt(d,h)&&(_=y(d),we(d,b,v,!0),d=null),h.patchFlag===-2&&(C=!1,h.dynamicChildren=null);const{type:S,ref:$,shapeFlag:P}=h;switch(S){case cs:U(d,h,m,_);break;case Ee:D(d,h,m,_);break;case Hn:d==null&&I(h,m,_,A);break;case Ze:O(d,h,m,_,b,v,A,x,C);break;default:P&1?j(d,h,m,_,b,v,A,x,C):P&6?Q(d,h,m,_,b,v,A,x,C):(P&64||P&128)&&S.process(d,h,m,_,b,v,A,x,C,N)}$!=null&&b?cn($,d&&d.ref,v,h||d,!h):$==null&&d&&d.ref!=null&&cn(d.ref,null,v,d,!0)},U=(d,h,m,_)=>{if(d==null)s(h.el=l(h.children),m,_);else{const b=h.el=d.el;h.children!==d.children&&u(b,h.children)}},D=(d,h,m,_)=>{d==null?s(h.el=c(h.children||""),m,_):h.el=d.el},I=(d,h,m,_)=>{[d.el,d.anchor]=T(d.children,h,m,_,d.el,d.anchor)},L=({el:d,anchor:h},m,_)=>{let b;for(;d&&d!==h;)b=p(d),s(d,m,_),d=b;s(h,m,_)},M=({el:d,anchor:h})=>{let m;for(;d&&d!==h;)m=p(d),r(d),d=m;r(h)},j=(d,h,m,_,b,v,A,x,C)=>{h.type==="svg"?A="svg":h.type==="math"&&(A="mathml"),d==null?G(h,m,_,b,v,A,x,C):w(d,h,b,v,A,x,C)},G=(d,h,m,_,b,v,A,x)=>{let C,S;const{props:$,shapeFlag:P,transition:H,dirs:K}=d;if(C=d.el=i(d.type,v,$&&$.is,$),P&8?a(C,d.children):P&16&&V(d.children,C,null,_,b,bs(d,v),A,x),K&&Et(d,null,_,"created"),Z(C,d,d.scopeId,A,_),$){for(const le in $)le!=="value"&&!sn(le)&&o(C,le,null,$[le],v,_);"value"in $&&o(C,"value",null,$.value,v),(S=$.onVnodeBeforeMount)&&Ke(S,_,d)}K&&Et(d,null,_,"beforeMount");const J=Kc(b,H);J&&H.beforeEnter(C),s(C,h,m),((S=$&&$.onVnodeMounted)||J||K)&&Te(()=>{S&&Ke(S,_,d),J&&H.enter(C),K&&Et(d,null,_,"mounted")},b)},Z=(d,h,m,_,b)=>{if(m&&g(d,m),_)for(let v=0;v<_.length;v++)g(d,_[v]);if(b){let v=b.subTree;if(h===v||ji(v.type)&&(v.ssContent===h||v.ssFallback===h)){const A=b.vnode;Z(d,A,A.scopeId,A.slotScopeIds,b.parent)}}},V=(d,h,m,_,b,v,A,x,C=0)=>{for(let S=C;S<d.length;S++){const $=d[S]=x?gt(d[S]):Ge(d[S]);E(null,$,h,m,_,b,v,A,x)}},w=(d,h,m,_,b,v,A)=>{const x=h.el=d.el;let{patchFlag:C,dynamicChildren:S,dirs:$}=h;C|=d.patchFlag&16;const P=d.props||oe,H=h.props||oe;let K;if(m&&wt(m,!1),(K=H.onVnodeBeforeUpdate)&&Ke(K,m,h,d),$&&Et(h,d,m,"beforeUpdate"),m&&wt(m,!0),(P.innerHTML&&H.innerHTML==null||P.textContent&&H.textContent==null)&&a(x,""),S?B(d.dynamicChildren,S,x,m,_,bs(h,b),v):A||X(d,h,x,null,m,_,bs(h,b),v,!1),C>0){if(C&16)z(x,P,H,m,b);else if(C&2&&P.class!==H.class&&o(x,"class",null,H.class,b),C&4&&o(x,"style",P.style,H.style,b),C&8){const J=h.dynamicProps;for(let le=0;le<J.length;le++){const ne=J[le],Ce=P[ne],xe=H[ne];(xe!==Ce||ne==="value")&&o(x,ne,Ce,xe,b,m)}}C&1&&d.children!==h.children&&a(x,h.children)}else!A&&S==null&&z(x,P,H,m,b);((K=H.onVnodeUpdated)||$)&&Te(()=>{K&&Ke(K,m,h,d),$&&Et(h,d,m,"updated")},_)},B=(d,h,m,_,b,v,A)=>{for(let x=0;x<h.length;x++){const C=d[x],S=h[x],$=C.el&&(C.type===Ze||!Rt(C,S)||C.shapeFlag&198)?f(C.el):m;E(C,S,$,null,_,b,v,A,!0)}},z=(d,h,m,_,b)=>{if(h!==m){if(h!==oe)for(const v in h)!sn(v)&&!(v in m)&&o(d,v,h[v],null,b,_);for(const v in m){if(sn(v))continue;const A=m[v],x=h[v];A!==x&&v!=="value"&&o(d,v,x,A,b,_)}"value"in m&&o(d,"value",h.value,m.value,b)}},O=(d,h,m,_,b,v,A,x,C)=>{const S=h.el=d?d.el:l(""),$=h.anchor=d?d.anchor:l("");let{patchFlag:P,dynamicChildren:H,slotScopeIds:K}=h;K&&(x=x?x.concat(K):K),d==null?(s(S,m,_),s($,m,_),V(h.children||[],m,$,b,v,A,x,C)):P>0&&P&64&&H&&d.dynamicChildren?(B(d.dynamicChildren,H,m,b,v,A,x),(h.key!=null||b&&h===b.subTree)&&ur(d,h,!0)):X(d,h,m,$,b,v,A,x,C)},Q=(d,h,m,_,b,v,A,x,C)=>{h.slotScopeIds=x,d==null?h.shapeFlag&512?b.ctx.activate(h,m,_,A,C):fe(h,m,_,b,v,A,C):ve(d,h,C)},fe=(d,h,m,_,b,v,A)=>{const x=d.component=ua(d,_,b);if(os(d)&&(x.ctx.renderer=N),fa(x,!1,A),x.asyncDep){if(b&&b.registerDep(x,se,A),!d.el){const C=x.subTree=ye(Ee);D(null,C,h,m),d.placeholder=C.el}}else se(x,d,h,m,b,v,A)},ve=(d,h,m)=>{const _=h.component=d.component;if(Xc(d,h,m))if(_.asyncDep&&!_.asyncResolved){W(_,h,m);return}else _.next=h,_.update();else h.el=d.el,_.vnode=h},se=(d,h,m,_,b,v,A)=>{const x=()=>{if(d.isMounted){let{next:P,bu:H,u:K,parent:J,vnode:le}=d;{const Be=Ni(d);if(Be){P&&(P.el=le.el,W(d,P,A)),Be.asyncDep.then(()=>{d.isUnmounted||x()});return}}let ne=P,Ce;wt(d,!1),P?(P.el=le.el,W(d,P,A)):P=le,H&&Ln(H),(Ce=P.props&&P.props.onVnodeBeforeUpdate)&&Ke(Ce,J,P,le),wt(d,!0);const xe=Nr(d),Ve=d.subTree;d.subTree=xe,E(Ve,xe,f(Ve.el),y(Ve),d,b,v),P.el=xe.el,ne===null&&ea(d,xe.el),K&&Te(K,b),(Ce=P.props&&P.props.onVnodeUpdated)&&Te(()=>Ke(Ce,J,P,le),b)}else{let P;const{el:H,props:K}=h,{bm:J,m:le,parent:ne,root:Ce,type:xe}=d,Ve=an(h);wt(d,!1),J&&Ln(J),!Ve&&(P=K&&K.onVnodeBeforeMount)&&Ke(P,ne,h),wt(d,!0);{Ce.ce&&Ce.ce._def.shadowRoot!==!1&&Ce.ce._injectChildStyle(xe);const Be=d.subTree=Nr(d);E(null,Be,m,_,d,b,v),h.el=Be.el}if(le&&Te(le,b),!Ve&&(P=K&&K.onVnodeMounted)){const Be=h;Te(()=>Ke(P,ne,Be),b)}(h.shapeFlag&256||ne&&an(ne.vnode)&&ne.vnode.shapeFlag&256)&&d.a&&Te(d.a,b),d.isMounted=!0,h=m=_=null}};d.scope.on();const C=d.effect=new jo(x);d.scope.off();const S=d.update=C.run.bind(C),$=d.job=C.runIfDirty.bind(C);$.i=d,$.id=d.uid,C.scheduler=()=>ir($),wt(d,!0),S()},W=(d,h,m)=>{h.component=d;const _=d.vnode.props;d.vnode=h,d.next=null,Fc(d,h.props,_,m),Vc(d,h.children,m),ot(),wr(d),it()},X=(d,h,m,_,b,v,A,x,C=!1)=>{const S=d&&d.children,$=d?d.shapeFlag:0,P=h.children,{patchFlag:H,shapeFlag:K}=h;if(H>0){if(H&128){ct(S,P,m,_,b,v,A,x,C);return}else if(H&256){Je(S,P,m,_,b,v,A,x,C);return}}K&8?($&16&&Oe(S,b,v),P!==S&&a(m,P)):$&16?K&16?ct(S,P,m,_,b,v,A,x,C):Oe(S,b,v,!0):($&8&&a(m,""),K&16&&V(P,m,_,b,v,A,x,C))},Je=(d,h,m,_,b,v,A,x,C)=>{d=d||Bt,h=h||Bt;const S=d.length,$=h.length,P=Math.min(S,$);let H;for(H=0;H<P;H++){const K=h[H]=C?gt(h[H]):Ge(h[H]);E(d[H],K,m,null,b,v,A,x,C)}S>$?Oe(d,b,v,!0,!1,P):V(h,m,_,b,v,A,x,C,P)},ct=(d,h,m,_,b,v,A,x,C)=>{let S=0;const $=h.length;let P=d.length-1,H=$-1;for(;S<=P&&S<=H;){const K=d[S],J=h[S]=C?gt(h[S]):Ge(h[S]);if(Rt(K,J))E(K,J,m,null,b,v,A,x,C);else break;S++}for(;S<=P&&S<=H;){const K=d[P],J=h[H]=C?gt(h[H]):Ge(h[H]);if(Rt(K,J))E(K,J,m,null,b,v,A,x,C);else break;P--,H--}if(S>P){if(S<=H){const K=H+1,J=K<$?h[K].el:_;for(;S<=H;)E(null,h[S]=C?gt(h[S]):Ge(h[S]),m,J,b,v,A,x,C),S++}}else if(S>H)for(;S<=P;)we(d[S],b,v,!0),S++;else{const K=S,J=S,le=new Map;for(S=J;S<=H;S++){const Ae=h[S]=C?gt(h[S]):Ge(h[S]);Ae.key!=null&&le.set(Ae.key,S)}let ne,Ce=0;const xe=H-J+1;let Ve=!1,Be=0;const Jt=new Array(xe);for(S=0;S<xe;S++)Jt[S]=0;for(S=K;S<=P;S++){const Ae=d[S];if(Ce>=xe){we(Ae,b,v,!0);continue}let Ue;if(Ae.key!=null)Ue=le.get(Ae.key);else for(ne=J;ne<=H;ne++)if(Jt[ne-J]===0&&Rt(Ae,h[ne])){Ue=ne;break}Ue===void 0?we(Ae,b,v,!0):(Jt[Ue-J]=S+1,Ue>=Be?Be=Ue:Ve=!0,E(Ae,h[Ue],m,null,b,v,A,x,C),Ce++)}const _r=Ve?qc(Jt):Bt;for(ne=_r.length-1,S=xe-1;S>=0;S--){const Ae=J+S,Ue=h[Ae],yr=h[Ae+1],vr=Ae+1<$?yr.el||yr.placeholder:_;Jt[S]===0?E(null,Ue,m,vr,b,v,A,x,C):Ve&&(ne<0||S!==_r[ne]?je(Ue,m,vr,2):ne--)}}},je=(d,h,m,_,b=null)=>{const{el:v,type:A,transition:x,children:C,shapeFlag:S}=d;if(S&6){je(d.component.subTree,h,m,_);return}if(S&128){d.suspense.move(h,m,_);return}if(S&64){A.move(d,h,m,N);return}if(A===Ze){s(v,h,m);for(let P=0;P<C.length;P++)je(C[P],h,m,_);s(d.anchor,h,m);return}if(A===Hn){L(d,h,m);return}if(_!==2&&S&1&&x)if(_===0)x.beforeEnter(v),s(v,h,m),Te(()=>x.enter(v),b);else{const{leave:P,delayLeave:H,afterLeave:K}=x,J=()=>{d.ctx.isUnmounted?r(v):s(v,h,m)},le=()=>{P(v,()=>{J(),K&&K()})};H?H(v,J,le):le()}else s(v,h,m)},we=(d,h,m,_=!1,b=!1)=>{const{type:v,props:A,ref:x,children:C,dynamicChildren:S,shapeFlag:$,patchFlag:P,dirs:H,cacheIndex:K}=d;if(P===-2&&(b=!1),x!=null&&(ot(),cn(x,null,m,d,!0),it()),K!=null&&(h.renderCache[K]=void 0),$&256){h.ctx.deactivate(d);return}const J=$&1&&H,le=!an(d);let ne;if(le&&(ne=A&&A.onVnodeBeforeUnmount)&&Ke(ne,h,d),$&6)Rn(d.component,m,_);else{if($&128){d.suspense.unmount(m,_);return}J&&Et(d,null,h,"beforeUnmount"),$&64?d.type.remove(d,h,m,N,_):S&&!S.hasOnce&&(v!==Ze||P>0&&P&64)?Oe(S,h,m,!1,!0):(v===Ze&&P&384||!b&&$&16)&&Oe(C,h,m),_&&Dt(d)}(le&&(ne=A&&A.onVnodeUnmounted)||J)&&Te(()=>{ne&&Ke(ne,h,d),J&&Et(d,null,h,"unmounted")},m)},Dt=d=>{const{type:h,el:m,anchor:_,transition:b}=d;if(h===Ze){Lt(m,_);return}if(h===Hn){M(d);return}const v=()=>{r(m),b&&!b.persisted&&b.afterLeave&&b.afterLeave()};if(d.shapeFlag&1&&b&&!b.persisted){const{leave:A,delayLeave:x}=b,C=()=>A(m,v);x?x(d.el,v,C):C()}else v()},Lt=(d,h)=>{let m;for(;d!==h;)m=p(d),r(d),d=m;r(h)},Rn=(d,h,m)=>{const{bum:_,scope:b,job:v,subTree:A,um:x,m:C,a:S,parent:$,slots:{__:P}}=d;Lr(C),Lr(S),_&&Ln(_),$&&F(P)&&P.forEach(H=>{$.renderCache[H]=void 0}),b.stop(),v&&(v.flags|=8,we(A,d,h,m)),x&&Te(x,h),Te(()=>{d.isUnmounted=!0},h),h&&h.pendingBranch&&!h.isUnmounted&&d.asyncDep&&!d.asyncResolved&&d.suspenseId===h.pendingId&&(h.deps--,h.deps===0&&h.resolve())},Oe=(d,h,m,_=!1,b=!1,v=0)=>{for(let A=v;A<d.length;A++)we(d[A],h,m,_,b)},y=d=>{if(d.shapeFlag&6)return y(d.component.subTree);if(d.shapeFlag&128)return d.suspense.next();const h=p(d.anchor||d.el),m=h&&h[ai];return m?p(m):h};let k=!1;const R=(d,h,m)=>{d==null?h._vnode&&we(h._vnode,null,null,!0):E(h._vnode||null,d,h,null,null,null,m),h._vnode=d,k||(k=!0,wr(),ii(),k=!1)},N={p:E,um:we,m:je,r:Dt,mt:fe,mc:V,pc:X,pbc:B,n:y,o:e};return{render:R,hydrate:void 0,createApp:Dc(R)}}function bs({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function wt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Kc(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function ur(e,t,n=!1){const s=e.children,r=t.children;if(F(s)&&F(r))for(let o=0;o<s.length;o++){const i=s[o];let l=r[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[o]=gt(r[o]),l.el=i.el),!n&&l.patchFlag!==-2&&ur(i,l)),l.type===cs&&(l.el=i.el),l.type===Ee&&!l.el&&(l.el=i.el)}}function qc(e){const t=e.slice(),n=[0];let s,r,o,i,l;const c=e.length;for(s=0;s<c;s++){const u=e[s];if(u!==0){if(r=n[n.length-1],e[r]<u){t[s]=r,n.push(s);continue}for(o=0,i=n.length-1;o<i;)l=o+i>>1,e[n[l]]<u?o=l+1:i=l;u<e[n[o]]&&(o>0&&(t[s]=n[o-1]),n[o]=s)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function Ni(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Ni(t)}function Lr(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Wc=Symbol.for("v-scx"),Zc=()=>Ie(Wc);function Wt(e,t,n){return Fi(e,t,n)}function Fi(e,t,n=oe){const{immediate:s,deep:r,flush:o,once:i}=n,l=de({},n),c=t&&s||!t&&o!=="post";let u;if(bn){if(o==="sync"){const g=Zc();u=g.__watcherHandles||(g.__watcherHandles=[])}else if(!c){const g=()=>{};return g.stop=ze,g.resume=ze,g.pause=ze,g}}const a=me;l.call=(g,T,E)=>He(g,a,T,E);let f=!1;o==="post"?l.scheduler=g=>{Te(g,a&&a.suspense)}:o!=="sync"&&(f=!0,l.scheduler=(g,T)=>{T?g():ir(g)}),l.augmentJob=g=>{t&&(g.flags|=4),f&&(g.flags|=2,a&&(g.id=a.uid,g.i=a))};const p=lc(e,t,l);return bn&&(u?u.push(p):c&&p()),p}function Gc(e,t,n){const s=this.proxy,r=ae(e)?e.includes(".")?Hi(s,e):()=>s[e]:e.bind(s,s);let o;q(t)?o=t:(o=t.handler,n=t);const i=An(this),l=Fi(r,o.bind(s),n);return i(),l}function Hi(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const zc=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Le(t)}Modifiers`]||e[`${St(t)}Modifiers`];function Qc(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||oe;let r=n;const o=t.startsWith("update:"),i=o&&zc(s,t.slice(7));i&&(i.trim&&(r=n.map(a=>ae(a)?a.trim():a)),i.number&&(r=n.map(Vn)));let l,c=s[l=hs(t)]||s[l=hs(Le(t))];!c&&o&&(c=s[l=hs(St(t))]),c&&He(c,e,6,r);const u=s[l+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,He(u,e,6,r)}}function $i(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const o=e.emits;let i={},l=!1;if(!q(e)){const c=u=>{const a=$i(u,t,!0);a&&(l=!0,de(i,a))};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}return!o&&!l?(ie(e)&&s.set(e,null),null):(F(o)?o.forEach(c=>i[c]=null):de(i,o),ie(e)&&s.set(e,i),i)}function ls(e,t){return!e||!Jn(t)?!1:(t=t.slice(2).replace(/Once$/,""),te(e,t[0].toLowerCase()+t.slice(1))||te(e,St(t))||te(e,t))}function Nr(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[o],slots:i,attrs:l,emit:c,render:u,renderCache:a,props:f,data:p,setupState:g,ctx:T,inheritAttrs:E}=e,U=Wn(e);let D,I;try{if(n.shapeFlag&4){const M=r||s,j=M;D=Ge(u.call(j,M,a,f,g,p,T)),I=l}else{const M=t;D=Ge(M.length>1?M(f,{attrs:l,slots:i,emit:c}):M(f,null)),I=t.props?l:Jc(l)}}catch(M){fn.length=0,ns(M,e,1),D=ye(Ee)}let L=D;if(I&&E!==!1){const M=Object.keys(I),{shapeFlag:j}=L;M.length&&j&7&&(o&&M.some(Zs)&&(I=Yc(I,o)),L=bt(L,I,!1,!0))}return n.dirs&&(L=bt(L,null,!1,!0),L.dirs=L.dirs?L.dirs.concat(n.dirs):n.dirs),n.transition&&yn(L,n.transition),D=L,Wn(U),D}const Jc=e=>{let t;for(const n in e)(n==="class"||n==="style"||Jn(n))&&((t||(t={}))[n]=e[n]);return t},Yc=(e,t)=>{const n={};for(const s in e)(!Zs(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function Xc(e,t,n){const{props:s,children:r,component:o}=e,{props:i,children:l,patchFlag:c}=t,u=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return s?Fr(s,i,u):!!i;if(c&8){const a=t.dynamicProps;for(let f=0;f<a.length;f++){const p=a[f];if(i[p]!==s[p]&&!ls(u,p))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:s===i?!1:s?i?Fr(s,i,u):!0:!!i;return!1}function Fr(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const o=s[r];if(t[o]!==e[o]&&!ls(n,o))return!0}return!1}function ea({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const ji=e=>e.__isSuspense;function ta(e,t){t&&t.pendingBranch?F(e)?t.effects.push(...e):t.effects.push(e):uc(e)}const Ze=Symbol.for("v-fgt"),cs=Symbol.for("v-txt"),Ee=Symbol.for("v-cmt"),Hn=Symbol.for("v-stc"),fn=[];let Pe=null;function Pt(e=!1){fn.push(Pe=e?null:[])}function na(){fn.pop(),Pe=fn[fn.length-1]||null}let vn=1;function Hr(e,t=!1){vn+=e,e<0&&Pe&&t&&(Pe.hasOnce=!0)}function Vi(e){return e.dynamicChildren=vn>0?Pe||Bt:null,na(),vn>0&&Pe&&Pe.push(e),e}function jt(e,t,n,s,r,o){return Vi(Vt(e,t,n,s,r,o,!0))}function sa(e,t,n,s,r){return Vi(ye(e,t,n,s,r,!0))}function Gn(e){return e?e.__v_isVNode===!0:!1}function Rt(e,t){return e.type===t.type&&e.key===t.key}const Bi=({key:e})=>e??null,$n=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?ae(e)||ue(e)||q(e)?{i:Re,r:e,k:t,f:!!n}:e:null);function Vt(e,t=null,n=null,s=0,r=null,o=e===Ze?0:1,i=!1,l=!1){const c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Bi(t),ref:t&&$n(t),scopeId:ci,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Re};return l?(fr(c,n),o&128&&e.normalize(c)):n&&(c.shapeFlag|=ae(n)?8:16),vn>0&&!i&&Pe&&(c.patchFlag>0||o&6)&&c.patchFlag!==32&&Pe.push(c),c}const ye=ra;function ra(e,t=null,n=null,s=0,r=null,o=!1){if((!e||e===Ei)&&(e=Ee),Gn(e)){const l=bt(e,t,!0);return n&&fr(l,n),vn>0&&!o&&Pe&&(l.shapeFlag&6?Pe[Pe.indexOf(e)]=l:Pe.push(l)),l.patchFlag=-2,l}if(ma(e)&&(e=e.__vccOpts),t){t=oa(t);let{class:l,style:c}=t;l&&!ae(l)&&(t.class=Kt(l)),ie(c)&&(rr(c)&&!F(c)&&(c=de({},c)),t.style=Qs(c))}const i=ae(e)?1:ji(e)?128:ui(e)?64:ie(e)?4:q(e)?2:0;return Vt(e,t,n,s,r,i,o,!0)}function oa(e){return e?rr(e)||Oi(e)?de({},e):e:null}function bt(e,t,n=!1,s=!1){const{props:r,ref:o,patchFlag:i,children:l,transition:c}=e,u=t?la(r||{},t):r,a={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&Bi(u),ref:t&&t.ref?n&&o?F(o)?o.concat($n(t)):[o,$n(t)]:$n(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ze?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&bt(e.ssContent),ssFallback:e.ssFallback&&bt(e.ssFallback),placeholder:e.placeholder,el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&s&&yn(a,c.clone(a)),a}function ia(e=" ",t=0){return ye(cs,null,e,t)}function Of(e,t){const n=ye(Hn,null,e);return n.staticCount=t,n}function Ui(e="",t=!1){return t?(Pt(),sa(Ee,null,e)):ye(Ee,null,e)}function Ge(e){return e==null||typeof e=="boolean"?ye(Ee):F(e)?ye(Ze,null,e.slice()):Gn(e)?gt(e):ye(cs,null,String(e))}function gt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:bt(e)}function fr(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(F(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),fr(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!Oi(t)?t._ctx=Re:r===3&&Re&&(Re.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else q(t)?(t={default:t,_ctx:Re},n=32):(t=String(t),s&64?(n=16,t=[ia(t)]):n=8);e.children=t,e.shapeFlag|=n}function la(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=Kt([t.class,s.class]));else if(r==="style")t.style=Qs([t.style,s.style]);else if(Jn(r)){const o=t[r],i=s[r];i&&o!==i&&!(F(o)&&o.includes(i))&&(t[r]=o?[].concat(o,i):i)}else r!==""&&(t[r]=s[r])}return t}function Ke(e,t,n,s=null){He(e,t,7,[n,s])}const ca=Ai();let aa=0;function ua(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||ca,o={uid:aa++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Fo(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:ki(s,r),emitsOptions:$i(s,r),emit:null,emitted:null,propsDefaults:oe,inheritAttrs:s.inheritAttrs,ctx:oe,data:oe,props:oe,attrs:oe,slots:oe,refs:oe,setupState:oe,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=Qc.bind(null,o),e.ce&&e.ce(o),o}let me=null;const dr=()=>me||Re;let zn,$s;{const e=es(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),o=>{r.length>1?r.forEach(i=>i(o)):r[0](o)}};zn=t("__VUE_INSTANCE_SETTERS__",n=>me=n),$s=t("__VUE_SSR_SETTERS__",n=>bn=n)}const An=e=>{const t=me;return zn(e),e.scope.on(),()=>{e.scope.off(),zn(t)}},$r=()=>{me&&me.scope.off(),zn(null)};function Ki(e){return e.vnode.shapeFlag&4}let bn=!1;function fa(e,t=!1,n=!1){t&&$s(t);const{props:s,children:r}=e.vnode,o=Ki(e);Nc(e,s,o,t),jc(e,r,n||t);const i=o?da(e,t):void 0;return t&&$s(!1),i}function da(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Ac);const{setup:s}=n;if(s){ot();const r=e.setupContext=s.length>1?pa(e):null,o=An(e),i=xn(s,e,0,[e.props,r]),l=Oo(i);if(it(),o(),(l||e.sp)&&!an(e)&&yi(e),l){if(i.then($r,$r),t)return i.then(c=>{jr(e,c)}).catch(c=>{ns(c,e,0)});e.asyncDep=i}else jr(e,i)}else qi(e)}function jr(e,t,n){q(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ie(t)&&(e.setupState=si(t)),qi(e)}function qi(e,t,n){const s=e.type;e.render||(e.render=s.render||ze);{const r=An(e);ot();try{Rc(e)}finally{it(),r()}}}const ha={get(e,t){return ge(e,"get",""),e[t]}};function pa(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,ha),slots:e.slots,emit:e.emit,expose:t}}function as(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(si(or(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in un)return un[n](e)},has(t,n){return n in t||n in un}})):e.proxy}function ga(e,t=!0){return q(e)?e.displayName||e.name:e.name||t&&e.__name}function ma(e){return q(e)&&"__vccOpts"in e}const _e=(e,t)=>oc(e,t,bn);function hr(e,t,n){const s=arguments.length;return s===2?ie(t)&&!F(t)?Gn(t)?ye(e,null,[t]):ye(e,t):ye(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&Gn(n)&&(n=[n]),ye(e,t,n))}const _a="3.5.18";/**
* @vue/runtime-dom v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let js;const Vr=typeof window<"u"&&window.trustedTypes;if(Vr)try{js=Vr.createPolicy("vue",{createHTML:e=>e})}catch{}const Wi=js?e=>js.createHTML(e):e=>e,ya="http://www.w3.org/2000/svg",va="http://www.w3.org/1998/Math/MathML",tt=typeof document<"u"?document:null,Br=tt&&tt.createElement("template"),ba={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?tt.createElementNS(ya,e):t==="mathml"?tt.createElementNS(va,e):n?tt.createElement(e,{is:n}):tt.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>tt.createTextNode(e),createComment:e=>tt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>tt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,o){const i=n?n.previousSibling:t.lastChild;if(r&&(r===o||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===o||!(r=r.nextSibling)););else{Br.innerHTML=Wi(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=Br.content;if(s==="svg"||s==="mathml"){const c=l.firstChild;for(;c.firstChild;)l.appendChild(c.firstChild);l.removeChild(c)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},at="transition",Xt="animation",Tn=Symbol("_vtc"),Zi={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Ta=de({},hi,Zi),Sa=e=>(e.displayName="Transition",e.props=Ta,e),Mf=Sa((e,{slots:t})=>hr(gc,Ea(e),t)),Ct=(e,t=[])=>{F(e)?e.forEach(n=>n(...t)):e&&e(...t)},Ur=e=>e?F(e)?e.some(t=>t.length>1):e.length>1:!1;function Ea(e){const t={};for(const O in e)O in Zi||(t[O]=e[O]);if(e.css===!1)return t;const{name:n="v",type:s,duration:r,enterFromClass:o=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:l=`${n}-enter-to`,appearFromClass:c=o,appearActiveClass:u=i,appearToClass:a=l,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:p=`${n}-leave-active`,leaveToClass:g=`${n}-leave-to`}=e,T=wa(r),E=T&&T[0],U=T&&T[1],{onBeforeEnter:D,onEnter:I,onEnterCancelled:L,onLeave:M,onLeaveCancelled:j,onBeforeAppear:G=D,onAppear:Z=I,onAppearCancelled:V=L}=t,w=(O,Q,fe,ve)=>{O._enterCancelled=ve,xt(O,Q?a:l),xt(O,Q?u:i),fe&&fe()},B=(O,Q)=>{O._isLeaving=!1,xt(O,f),xt(O,g),xt(O,p),Q&&Q()},z=O=>(Q,fe)=>{const ve=O?Z:I,se=()=>w(Q,O,fe);Ct(ve,[Q,se]),Kr(()=>{xt(Q,O?c:o),Xe(Q,O?a:l),Ur(ve)||qr(Q,s,E,se)})};return de(t,{onBeforeEnter(O){Ct(D,[O]),Xe(O,o),Xe(O,i)},onBeforeAppear(O){Ct(G,[O]),Xe(O,c),Xe(O,u)},onEnter:z(!1),onAppear:z(!0),onLeave(O,Q){O._isLeaving=!0;const fe=()=>B(O,Q);Xe(O,f),O._enterCancelled?(Xe(O,p),Gr()):(Gr(),Xe(O,p)),Kr(()=>{O._isLeaving&&(xt(O,f),Xe(O,g),Ur(M)||qr(O,s,U,fe))}),Ct(M,[O,fe])},onEnterCancelled(O){w(O,!1,void 0,!0),Ct(L,[O])},onAppearCancelled(O){w(O,!0,void 0,!0),Ct(V,[O])},onLeaveCancelled(O){B(O),Ct(j,[O])}})}function wa(e){if(e==null)return null;if(ie(e))return[Ts(e.enter),Ts(e.leave)];{const t=Ts(e);return[t,t]}}function Ts(e){return wl(e)}function Xe(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[Tn]||(e[Tn]=new Set)).add(t)}function xt(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.remove(s));const n=e[Tn];n&&(n.delete(t),n.size||(e[Tn]=void 0))}function Kr(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Ca=0;function qr(e,t,n,s){const r=e._endId=++Ca,o=()=>{r===e._endId&&s()};if(n!=null)return setTimeout(o,n);const{type:i,timeout:l,propCount:c}=xa(e,t);if(!i)return s();const u=i+"end";let a=0;const f=()=>{e.removeEventListener(u,p),o()},p=g=>{g.target===e&&++a>=c&&f()};setTimeout(()=>{a<c&&f()},l+1),e.addEventListener(u,p)}function xa(e,t){const n=window.getComputedStyle(e),s=T=>(n[T]||"").split(", "),r=s(`${at}Delay`),o=s(`${at}Duration`),i=Wr(r,o),l=s(`${Xt}Delay`),c=s(`${Xt}Duration`),u=Wr(l,c);let a=null,f=0,p=0;t===at?i>0&&(a=at,f=i,p=o.length):t===Xt?u>0&&(a=Xt,f=u,p=c.length):(f=Math.max(i,u),a=f>0?i>u?at:Xt:null,p=a?a===at?o.length:c.length:0);const g=a===at&&/\b(transform|all)(,|$)/.test(s(`${at}Property`).toString());return{type:a,timeout:f,propCount:p,hasTransform:g}}function Wr(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,s)=>Zr(n)+Zr(e[s])))}function Zr(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function Gr(){return document.body.offsetHeight}function Aa(e,t,n){const s=e[Tn];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Qn=Symbol("_vod"),Gi=Symbol("_vsh"),kf={beforeMount(e,{value:t},{transition:n}){e[Qn]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):en(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:s}){!t!=!n&&(s?t?(s.beforeEnter(e),en(e,!0),s.enter(e)):s.leave(e,()=>{en(e,!1)}):en(e,t))},beforeUnmount(e,{value:t}){en(e,t)}};function en(e,t){e.style.display=t?e[Qn]:"none",e[Gi]=!t}const Ra=Symbol(""),Pa=/(^|;)\s*display\s*:/;function Oa(e,t,n){const s=e.style,r=ae(n);let o=!1;if(n&&!r){if(t)if(ae(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&jn(s,l,"")}else for(const i in t)n[i]==null&&jn(s,i,"");for(const i in n)i==="display"&&(o=!0),jn(s,i,n[i])}else if(r){if(t!==n){const i=s[Ra];i&&(n+=";"+i),s.cssText=n,o=Pa.test(n)}}else t&&e.removeAttribute("style");Qn in e&&(e[Qn]=o?s.display:"",e[Gi]&&(s.display="none"))}const zr=/\s*!important$/;function jn(e,t,n){if(F(n))n.forEach(s=>jn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=Ma(e,t);zr.test(n)?e.setProperty(St(s),n.replace(zr,""),"important"):e[s]=n}}const Qr=["Webkit","Moz","ms"],Ss={};function Ma(e,t){const n=Ss[t];if(n)return n;let s=Le(t);if(s!=="filter"&&s in e)return Ss[t]=s;s=Xn(s);for(let r=0;r<Qr.length;r++){const o=Qr[r]+s;if(o in e)return Ss[t]=o}return t}const Jr="http://www.w3.org/1999/xlink";function Yr(e,t,n,s,r,o=Ol(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Jr,t.slice(6,t.length)):e.setAttributeNS(Jr,t,n):n==null||o&&!Io(n)?e.removeAttribute(t):e.setAttribute(t,o?"":Qe(n)?String(n):n)}function Xr(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Wi(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,c=n==null?e.type==="checkbox"?"on":"":String(n);(l!==c||!("_value"in e))&&(e.value=c),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=Io(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(r||t)}function rt(e,t,n,s){e.addEventListener(t,n,s)}function ka(e,t,n,s){e.removeEventListener(t,n,s)}const eo=Symbol("_vei");function Ia(e,t,n,s,r=null){const o=e[eo]||(e[eo]={}),i=o[t];if(s&&i)i.value=s;else{const[l,c]=Da(t);if(s){const u=o[t]=Fa(s,r);rt(e,l,u,c)}else i&&(ka(e,l,i,c),o[t]=void 0)}}const to=/(?:Once|Passive|Capture)$/;function Da(e){let t;if(to.test(e)){t={};let s;for(;s=e.match(to);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):St(e.slice(2)),t]}let Es=0;const La=Promise.resolve(),Na=()=>Es||(La.then(()=>Es=0),Es=Date.now());function Fa(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;He(Ha(s,n.value),t,5,[s])};return n.value=e,n.attached=Na(),n}function Ha(e,t){if(F(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const no=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,$a=(e,t,n,s,r,o)=>{const i=r==="svg";t==="class"?Aa(e,s,i):t==="style"?Oa(e,n,s):Jn(t)?Zs(t)||Ia(e,t,n,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):ja(e,t,s,i))?(Xr(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Yr(e,t,s,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!ae(s))?Xr(e,Le(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),Yr(e,t,s,i))};function ja(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&no(t)&&q(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return no(t)&&ae(n)?!1:t in e}const Tt=e=>{const t=e.props["onUpdate:modelValue"]||!1;return F(t)?n=>Ln(t,n):t};function Va(e){e.target.composing=!0}function so(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const De=Symbol("_assign"),ro={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[De]=Tt(r);const o=s||r.props&&r.props.type==="number";rt(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;n&&(l=l.trim()),o&&(l=Vn(l)),e[De](l)}),n&&rt(e,"change",()=>{e.value=e.value.trim()}),t||(rt(e,"compositionstart",Va),rt(e,"compositionend",so),rt(e,"change",so))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:o}},i){if(e[De]=Tt(i),e.composing)return;const l=(o||e.type==="number")&&!/^0\d/.test(e.value)?Vn(e.value):e.value,c=t??"";l!==c&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||r&&e.value.trim()===c)||(e.value=c))}},Ba={deep:!0,created(e,t,n){e[De]=Tt(n),rt(e,"change",()=>{const s=e._modelValue,r=Zt(e),o=e.checked,i=e[De];if(F(s)){const l=Js(s,r),c=l!==-1;if(o&&!c)i(s.concat(r));else if(!o&&c){const u=[...s];u.splice(l,1),i(u)}}else if(Qt(s)){const l=new Set(s);o?l.add(r):l.delete(r),i(l)}else i(zi(e,o))})},mounted:oo,beforeUpdate(e,t,n){e[De]=Tt(n),oo(e,t,n)}};function oo(e,{value:t,oldValue:n},s){e._modelValue=t;let r;if(F(t))r=Js(t,s.props.value)>-1;else if(Qt(t))r=t.has(s.props.value);else{if(t===n)return;r=kt(t,zi(e,!0))}e.checked!==r&&(e.checked=r)}const Ua={created(e,{value:t},n){e.checked=kt(t,n.props.value),e[De]=Tt(n),rt(e,"change",()=>{e[De](Zt(e))})},beforeUpdate(e,{value:t,oldValue:n},s){e[De]=Tt(s),t!==n&&(e.checked=kt(t,s.props.value))}},Ka={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const r=Qt(t);rt(e,"change",()=>{const o=Array.prototype.filter.call(e.options,i=>i.selected).map(i=>n?Vn(Zt(i)):Zt(i));e[De](e.multiple?r?new Set(o):o:o[0]),e._assigning=!0,ss(()=>{e._assigning=!1})}),e[De]=Tt(s)},mounted(e,{value:t}){io(e,t)},beforeUpdate(e,t,n){e[De]=Tt(n)},updated(e,{value:t}){e._assigning||io(e,t)}};function io(e,t){const n=e.multiple,s=F(t);if(!(n&&!s&&!Qt(t))){for(let r=0,o=e.options.length;r<o;r++){const i=e.options[r],l=Zt(i);if(n)if(s){const c=typeof l;c==="string"||c==="number"?i.selected=t.some(u=>String(u)===String(l)):i.selected=Js(t,l)>-1}else i.selected=t.has(l);else if(kt(Zt(i),t)){e.selectedIndex!==r&&(e.selectedIndex=r);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function Zt(e){return"_value"in e?e._value:e.value}function zi(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const If={created(e,t,n){Dn(e,t,n,null,"created")},mounted(e,t,n){Dn(e,t,n,null,"mounted")},beforeUpdate(e,t,n,s){Dn(e,t,n,s,"beforeUpdate")},updated(e,t,n,s){Dn(e,t,n,s,"updated")}};function qa(e,t){switch(e){case"SELECT":return Ka;case"TEXTAREA":return ro;default:switch(t){case"checkbox":return Ba;case"radio":return Ua;default:return ro}}}function Dn(e,t,n,s,r){const i=qa(e.tagName,n.props&&n.props.type)[r];i&&i(e,t,n,s)}const Wa=["ctrl","shift","alt","meta"],Za={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Wa.some(n=>e[`${n}Key`]&&!t.includes(n))},Df=(e,t)=>{const n=e._withMods||(e._withMods={}),s=t.join(".");return n[s]||(n[s]=(r,...o)=>{for(let i=0;i<t.length;i++){const l=Za[t[i]];if(l&&l(r,t))return}return e(r,...o)})},Ga={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},Lf=(e,t)=>{const n=e._withKeys||(e._withKeys={}),s=t.join(".");return n[s]||(n[s]=r=>{if(!("key"in r))return;const o=St(r.key);if(t.some(i=>i===o||Ga[i]===o))return e(r)})},za=de({patchProp:$a},ba);let lo;function Qa(){return lo||(lo=Bc(za))}const Ja=(...e)=>{const t=Qa().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=Xa(s);if(!r)return;const o=t._component;!q(o)&&!o.render&&!o.template&&(o.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=n(r,!1,Ya(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function Ya(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Xa(e){return ae(e)?document.querySelector(e):e}/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let Qi;const us=e=>Qi=e,Ji=Symbol();function Vs(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var dn;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(dn||(dn={}));function eu(){const e=Ho(!0),t=e.run(()=>It({}));let n=[],s=[];const r=or({install(o){us(r),r._a=o,o.provide(Ji,r),o.config.globalProperties.$pinia=r,s.forEach(i=>n.push(i)),s=[]},use(o){return this._a?n.push(o):s.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}const Yi=()=>{};function co(e,t,n,s=Yi){e.push(t);const r=()=>{const o=e.indexOf(t);o>-1&&(e.splice(o,1),s())};return!n&&$o()&&kl(r),r}function Ft(e,...t){e.slice().forEach(n=>{n(...t)})}const tu=e=>e(),ao=Symbol(),ws=Symbol();function Bs(e,t){e instanceof Map&&t instanceof Map?t.forEach((n,s)=>e.set(s,n)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const n in t){if(!t.hasOwnProperty(n))continue;const s=t[n],r=e[n];Vs(r)&&Vs(s)&&e.hasOwnProperty(n)&&!ue(s)&&!yt(s)?e[n]=Bs(r,s):e[n]=s}return e}const nu=Symbol();function su(e){return!Vs(e)||!e.hasOwnProperty(nu)}const{assign:dt}=Object;function ru(e){return!!(ue(e)&&e.effect)}function ou(e,t,n,s){const{state:r,actions:o,getters:i}=t,l=n.state.value[e];let c;function u(){l||(n.state.value[e]=r?r():{});const a=tc(n.state.value[e]);return dt(a,o,Object.keys(i||{}).reduce((f,p)=>(f[p]=or(_e(()=>{us(n);const g=n._s.get(e);return i[p].call(g,g)})),f),{}))}return c=Xi(e,u,t,n,s,!0),c}function Xi(e,t,n={},s,r,o){let i;const l=dt({actions:{}},n),c={deep:!0};let u,a,f=[],p=[],g;const T=s.state.value[e];!o&&!T&&(s.state.value[e]={}),It({});let E;function U(V){let w;u=a=!1,typeof V=="function"?(V(s.state.value[e]),w={type:dn.patchFunction,storeId:e,events:g}):(Bs(s.state.value[e],V),w={type:dn.patchObject,payload:V,storeId:e,events:g});const B=E=Symbol();ss().then(()=>{E===B&&(u=!0)}),a=!0,Ft(f,w,s.state.value[e])}const D=o?function(){const{state:w}=n,B=w?w():{};this.$patch(z=>{dt(z,B)})}:Yi;function I(){i.stop(),f=[],p=[],s._s.delete(e)}const L=(V,w="")=>{if(ao in V)return V[ws]=w,V;const B=function(){us(s);const z=Array.from(arguments),O=[],Q=[];function fe(W){O.push(W)}function ve(W){Q.push(W)}Ft(p,{args:z,name:B[ws],store:j,after:fe,onError:ve});let se;try{se=V.apply(this&&this.$id===e?this:j,z)}catch(W){throw Ft(Q,W),W}return se instanceof Promise?se.then(W=>(Ft(O,W),W)).catch(W=>(Ft(Q,W),Promise.reject(W))):(Ft(O,se),se)};return B[ao]=!0,B[ws]=w,B},M={_p:s,$id:e,$onAction:co.bind(null,p),$patch:U,$reset:D,$subscribe(V,w={}){const B=co(f,V,w.detached,()=>z()),z=i.run(()=>Wt(()=>s.state.value[e],O=>{(w.flush==="sync"?a:u)&&V({storeId:e,type:dn.direct,events:g},O)},dt({},c,w)));return B},$dispose:I},j=Cn(M);s._s.set(e,j);const Z=(s._a&&s._a.runWithContext||tu)(()=>s._e.run(()=>(i=Ho()).run(()=>t({action:L}))));for(const V in Z){const w=Z[V];if(ue(w)&&!ru(w)||yt(w))o||(T&&su(w)&&(ue(w)?w.value=T[V]:Bs(w,T[V])),s.state.value[e][V]=w);else if(typeof w=="function"){const B=L(w,V);Z[V]=B,l.actions[V]=w}}return dt(j,Z),dt(Y(j),Z),Object.defineProperty(j,"$state",{get:()=>s.state.value[e],set:V=>{U(w=>{dt(w,V)})}}),s._p.forEach(V=>{dt(j,i.run(()=>V({store:j,app:s._a,pinia:s,options:l})))}),T&&o&&n.hydrate&&n.hydrate(j.$state,T),u=!0,a=!0,j}/*! #__NO_SIDE_EFFECTS__ */function el(e,t,n){let s,r;const o=typeof t=="function";typeof e=="string"?(s=e,r=o?n:t):(r=e,s=e.id);function i(l,c){const u=Lc();return l=l||(u?Ie(Ji,null):null),l&&us(l),l=Qi,l._s.has(s)||(o?Xi(s,t,r,l):ou(s,r,l)),l._s.get(s)}return i.$id=s,i}const iu="modulepreload",lu=function(e){return"/"+e},uo={},qe=function(t,n,s){let r=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),l=(i==null?void 0:i.nonce)||(i==null?void 0:i.getAttribute("nonce"));r=Promise.allSettled(n.map(c=>{if(c=lu(c),c in uo)return;uo[c]=!0;const u=c.endsWith(".css"),a=u?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${c}"]${a}`))return;const f=document.createElement("link");if(f.rel=u?"stylesheet":iu,u||(f.as="script"),f.crossOrigin="",f.href=c,l&&f.setAttribute("nonce",l),document.head.appendChild(f),u)return new Promise((p,g)=>{f.addEventListener("load",p),f.addEventListener("error",()=>g(new Error(`Unable to preload CSS for ${c}`)))})}))}function o(i){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=i,window.dispatchEvent(l),!l.defaultPrevented)throw i}return r.then(i=>{for(const l of i||[])l.status==="rejected"&&o(l.reason);return t().catch(o)})};/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const $t=typeof document<"u";function tl(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function cu(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&tl(e.default)}const ee=Object.assign;function Cs(e,t){const n={};for(const s in t){const r=t[s];n[s]=$e(r)?r.map(e):e(r)}return n}const hn=()=>{},$e=Array.isArray,nl=/#/g,au=/&/g,uu=/\//g,fu=/=/g,du=/\?/g,sl=/\+/g,hu=/%5B/g,pu=/%5D/g,rl=/%5E/g,gu=/%60/g,ol=/%7B/g,mu=/%7C/g,il=/%7D/g,_u=/%20/g;function pr(e){return encodeURI(""+e).replace(mu,"|").replace(hu,"[").replace(pu,"]")}function yu(e){return pr(e).replace(ol,"{").replace(il,"}").replace(rl,"^")}function Us(e){return pr(e).replace(sl,"%2B").replace(_u,"+").replace(nl,"%23").replace(au,"%26").replace(gu,"`").replace(ol,"{").replace(il,"}").replace(rl,"^")}function vu(e){return Us(e).replace(fu,"%3D")}function bu(e){return pr(e).replace(nl,"%23").replace(du,"%3F")}function Tu(e){return e==null?"":bu(e).replace(uu,"%2F")}function Sn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Su=/\/$/,Eu=e=>e.replace(Su,"");function xs(e,t,n="/"){let s,r={},o="",i="";const l=t.indexOf("#");let c=t.indexOf("?");return l<c&&l>=0&&(c=-1),c>-1&&(s=t.slice(0,c),o=t.slice(c+1,l>-1?l:t.length),r=e(o)),l>-1&&(s=s||t.slice(0,l),i=t.slice(l,t.length)),s=Au(s??t,n),{fullPath:s+(o&&"?")+o+i,path:s,query:r,hash:Sn(i)}}function wu(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function fo(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Cu(e,t,n){const s=t.matched.length-1,r=n.matched.length-1;return s>-1&&s===r&&Gt(t.matched[s],n.matched[r])&&ll(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Gt(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function ll(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!xu(e[n],t[n]))return!1;return!0}function xu(e,t){return $e(e)?ho(e,t):$e(t)?ho(t,e):e===t}function ho(e,t){return $e(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function Au(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),r=s[s.length-1];(r===".."||r===".")&&s.push("");let o=n.length-1,i,l;for(i=0;i<s.length;i++)if(l=s[i],l!==".")if(l==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+s.slice(i).join("/")}const ut={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var En;(function(e){e.pop="pop",e.push="push"})(En||(En={}));var pn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(pn||(pn={}));function Ru(e){if(!e)if($t){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Eu(e)}const Pu=/^[^#]+#/;function Ou(e,t){return e.replace(Pu,"#")+t}function Mu(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const fs=()=>({left:window.scrollX,top:window.scrollY});function ku(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=Mu(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function po(e,t){return(history.state?history.state.position-t:-1)+e}const Ks=new Map;function Iu(e,t){Ks.set(e,t)}function Du(e){const t=Ks.get(e);return Ks.delete(e),t}let Lu=()=>location.protocol+"//"+location.host;function cl(e,t){const{pathname:n,search:s,hash:r}=t,o=e.indexOf("#");if(o>-1){let l=r.includes(e.slice(o))?e.slice(o).length:1,c=r.slice(l);return c[0]!=="/"&&(c="/"+c),fo(c,"")}return fo(n,e)+s+r}function Nu(e,t,n,s){let r=[],o=[],i=null;const l=({state:p})=>{const g=cl(e,location),T=n.value,E=t.value;let U=0;if(p){if(n.value=g,t.value=p,i&&i===T){i=null;return}U=E?p.position-E.position:0}else s(g);r.forEach(D=>{D(n.value,T,{delta:U,type:En.pop,direction:U?U>0?pn.forward:pn.back:pn.unknown})})};function c(){i=n.value}function u(p){r.push(p);const g=()=>{const T=r.indexOf(p);T>-1&&r.splice(T,1)};return o.push(g),g}function a(){const{history:p}=window;p.state&&p.replaceState(ee({},p.state,{scroll:fs()}),"")}function f(){for(const p of o)p();o=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",a)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",a,{passive:!0}),{pauseListeners:c,listen:u,destroy:f}}function go(e,t,n,s=!1,r=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:r?fs():null}}function Fu(e){const{history:t,location:n}=window,s={value:cl(e,n)},r={value:t.state};r.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(c,u,a){const f=e.indexOf("#"),p=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+c:Lu()+e+c;try{t[a?"replaceState":"pushState"](u,"",p),r.value=u}catch(g){console.error(g),n[a?"replace":"assign"](p)}}function i(c,u){const a=ee({},t.state,go(r.value.back,c,r.value.forward,!0),u,{position:r.value.position});o(c,a,!0),s.value=c}function l(c,u){const a=ee({},r.value,t.state,{forward:c,scroll:fs()});o(a.current,a,!0);const f=ee({},go(s.value,c,null),{position:a.position+1},u);o(c,f,!1),s.value=c}return{location:s,state:r,push:l,replace:i}}function Hu(e){e=Ru(e);const t=Fu(e),n=Nu(e,t.state,t.location,t.replace);function s(o,i=!0){i||n.pauseListeners(),history.go(o)}const r=ee({location:"",base:e,go:s,createHref:Ou.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function $u(e){return typeof e=="string"||e&&typeof e=="object"}function al(e){return typeof e=="string"||typeof e=="symbol"}const ul=Symbol("");var mo;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(mo||(mo={}));function zt(e,t){return ee(new Error,{type:e,[ul]:!0},t)}function et(e,t){return e instanceof Error&&ul in e&&(t==null||!!(e.type&t))}const _o="[^/]+?",ju={sensitive:!1,strict:!1,start:!0,end:!0},Vu=/[.+*?^${}()[\]/\\]/g;function Bu(e,t){const n=ee({},ju,t),s=[];let r=n.start?"^":"";const o=[];for(const u of e){const a=u.length?[]:[90];n.strict&&!u.length&&(r+="/");for(let f=0;f<u.length;f++){const p=u[f];let g=40+(n.sensitive?.25:0);if(p.type===0)f||(r+="/"),r+=p.value.replace(Vu,"\\$&"),g+=40;else if(p.type===1){const{value:T,repeatable:E,optional:U,regexp:D}=p;o.push({name:T,repeatable:E,optional:U});const I=D||_o;if(I!==_o){g+=10;try{new RegExp(`(${I})`)}catch(M){throw new Error(`Invalid custom RegExp for param "${T}" (${I}): `+M.message)}}let L=E?`((?:${I})(?:/(?:${I}))*)`:`(${I})`;f||(L=U&&u.length<2?`(?:/${L})`:"/"+L),U&&(L+="?"),r+=L,g+=20,U&&(g+=-8),E&&(g+=-20),I===".*"&&(g+=-50)}a.push(g)}s.push(a)}if(n.strict&&n.end){const u=s.length-1;s[u][s[u].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const i=new RegExp(r,n.sensitive?"":"i");function l(u){const a=u.match(i),f={};if(!a)return null;for(let p=1;p<a.length;p++){const g=a[p]||"",T=o[p-1];f[T.name]=g&&T.repeatable?g.split("/"):g}return f}function c(u){let a="",f=!1;for(const p of e){(!f||!a.endsWith("/"))&&(a+="/"),f=!1;for(const g of p)if(g.type===0)a+=g.value;else if(g.type===1){const{value:T,repeatable:E,optional:U}=g,D=T in u?u[T]:"";if($e(D)&&!E)throw new Error(`Provided param "${T}" is an array but it is not repeatable (* or + modifiers)`);const I=$e(D)?D.join("/"):D;if(!I)if(U)p.length<2&&(a.endsWith("/")?a=a.slice(0,-1):f=!0);else throw new Error(`Missing required param "${T}"`);a+=I}}return a||"/"}return{re:i,score:s,keys:o,parse:l,stringify:c}}function Uu(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function fl(e,t){let n=0;const s=e.score,r=t.score;for(;n<s.length&&n<r.length;){const o=Uu(s[n],r[n]);if(o)return o;n++}if(Math.abs(r.length-s.length)===1){if(yo(s))return 1;if(yo(r))return-1}return r.length-s.length}function yo(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Ku={type:0,value:""},qu=/[a-zA-Z0-9_]/;function Wu(e){if(!e)return[[]];if(e==="/")return[[Ku]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(g){throw new Error(`ERR (${n})/"${u}": ${g}`)}let n=0,s=n;const r=[];let o;function i(){o&&r.push(o),o=[]}let l=0,c,u="",a="";function f(){u&&(n===0?o.push({type:0,value:u}):n===1||n===2||n===3?(o.length>1&&(c==="*"||c==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:u,regexp:a,repeatable:c==="*"||c==="+",optional:c==="*"||c==="?"})):t("Invalid state to consume buffer"),u="")}function p(){u+=c}for(;l<e.length;){if(c=e[l++],c==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:c==="/"?(u&&f(),i()):c===":"?(f(),n=1):p();break;case 4:p(),n=s;break;case 1:c==="("?n=2:qu.test(c)?p():(f(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--);break;case 2:c===")"?a[a.length-1]=="\\"?a=a.slice(0,-1)+c:n=3:a+=c;break;case 3:f(),n=0,c!=="*"&&c!=="?"&&c!=="+"&&l--,a="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${u}"`),f(),i(),r}function Zu(e,t,n){const s=Bu(Wu(e.path),n),r=ee(s,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function Gu(e,t){const n=[],s=new Map;t=So({strict:!1,end:!0,sensitive:!1},t);function r(f){return s.get(f)}function o(f,p,g){const T=!g,E=bo(f);E.aliasOf=g&&g.record;const U=So(t,f),D=[E];if("alias"in f){const M=typeof f.alias=="string"?[f.alias]:f.alias;for(const j of M)D.push(bo(ee({},E,{components:g?g.record.components:E.components,path:j,aliasOf:g?g.record:E})))}let I,L;for(const M of D){const{path:j}=M;if(p&&j[0]!=="/"){const G=p.record.path,Z=G[G.length-1]==="/"?"":"/";M.path=p.record.path+(j&&Z+j)}if(I=Zu(M,p,U),g?g.alias.push(I):(L=L||I,L!==I&&L.alias.push(I),T&&f.name&&!To(I)&&i(f.name)),dl(I)&&c(I),E.children){const G=E.children;for(let Z=0;Z<G.length;Z++)o(G[Z],I,g&&g.children[Z])}g=g||I}return L?()=>{i(L)}:hn}function i(f){if(al(f)){const p=s.get(f);p&&(s.delete(f),n.splice(n.indexOf(p),1),p.children.forEach(i),p.alias.forEach(i))}else{const p=n.indexOf(f);p>-1&&(n.splice(p,1),f.record.name&&s.delete(f.record.name),f.children.forEach(i),f.alias.forEach(i))}}function l(){return n}function c(f){const p=Ju(f,n);n.splice(p,0,f),f.record.name&&!To(f)&&s.set(f.record.name,f)}function u(f,p){let g,T={},E,U;if("name"in f&&f.name){if(g=s.get(f.name),!g)throw zt(1,{location:f});U=g.record.name,T=ee(vo(p.params,g.keys.filter(L=>!L.optional).concat(g.parent?g.parent.keys.filter(L=>L.optional):[]).map(L=>L.name)),f.params&&vo(f.params,g.keys.map(L=>L.name))),E=g.stringify(T)}else if(f.path!=null)E=f.path,g=n.find(L=>L.re.test(E)),g&&(T=g.parse(E),U=g.record.name);else{if(g=p.name?s.get(p.name):n.find(L=>L.re.test(p.path)),!g)throw zt(1,{location:f,currentLocation:p});U=g.record.name,T=ee({},p.params,f.params),E=g.stringify(T)}const D=[];let I=g;for(;I;)D.unshift(I.record),I=I.parent;return{name:U,path:E,params:T,matched:D,meta:Qu(D)}}e.forEach(f=>o(f));function a(){n.length=0,s.clear()}return{addRoute:o,resolve:u,removeRoute:i,clearRoutes:a,getRoutes:l,getRecordMatcher:r}}function vo(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function bo(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:zu(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function zu(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function To(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Qu(e){return e.reduce((t,n)=>ee(t,n.meta),{})}function So(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function Ju(e,t){let n=0,s=t.length;for(;n!==s;){const o=n+s>>1;fl(e,t[o])<0?s=o:n=o+1}const r=Yu(e);return r&&(s=t.lastIndexOf(r,s-1)),s}function Yu(e){let t=e;for(;t=t.parent;)if(dl(t)&&fl(e,t)===0)return t}function dl({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Xu(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const o=s[r].replace(sl," "),i=o.indexOf("="),l=Sn(i<0?o:o.slice(0,i)),c=i<0?null:Sn(o.slice(i+1));if(l in t){let u=t[l];$e(u)||(u=t[l]=[u]),u.push(c)}else t[l]=c}return t}function Eo(e){let t="";for(let n in e){const s=e[n];if(n=vu(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}($e(s)?s.map(o=>o&&Us(o)):[s&&Us(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function ef(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=$e(s)?s.map(r=>r==null?null:""+r):s==null?s:""+s)}return t}const tf=Symbol(""),wo=Symbol(""),ds=Symbol(""),gr=Symbol(""),qs=Symbol("");function tn(){let e=[];function t(s){return e.push(s),()=>{const r=e.indexOf(s);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function mt(e,t,n,s,r,o=i=>i()){const i=s&&(s.enterCallbacks[r]=s.enterCallbacks[r]||[]);return()=>new Promise((l,c)=>{const u=p=>{p===!1?c(zt(4,{from:n,to:t})):p instanceof Error?c(p):$u(p)?c(zt(2,{from:t,to:p})):(i&&s.enterCallbacks[r]===i&&typeof p=="function"&&i.push(p),l())},a=o(()=>e.call(s&&s.instances[r],t,n,u));let f=Promise.resolve(a);e.length<3&&(f=f.then(u)),f.catch(p=>c(p))})}function As(e,t,n,s,r=o=>o()){const o=[];for(const i of e)for(const l in i.components){let c=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(tl(c)){const a=(c.__vccOpts||c)[t];a&&o.push(mt(a,n,s,i,l,r))}else{let u=c();o.push(()=>u.then(a=>{if(!a)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const f=cu(a)?a.default:a;i.mods[l]=a,i.components[l]=f;const g=(f.__vccOpts||f)[t];return g&&mt(g,n,s,i,l,r)()}))}}return o}function Co(e){const t=Ie(ds),n=Ie(gr),s=_e(()=>{const c=Ne(e.to);return t.resolve(c)}),r=_e(()=>{const{matched:c}=s.value,{length:u}=c,a=c[u-1],f=n.matched;if(!a||!f.length)return-1;const p=f.findIndex(Gt.bind(null,a));if(p>-1)return p;const g=xo(c[u-2]);return u>1&&xo(a)===g&&f[f.length-1].path!==g?f.findIndex(Gt.bind(null,c[u-2])):p}),o=_e(()=>r.value>-1&&lf(n.params,s.value.params)),i=_e(()=>r.value>-1&&r.value===n.matched.length-1&&ll(n.params,s.value.params));function l(c={}){if(of(c)){const u=t[Ne(e.replace)?"replace":"push"](Ne(e.to)).catch(hn);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>u),u}return Promise.resolve()}return{route:s,href:_e(()=>s.value.href),isActive:o,isExactActive:i,navigate:l}}function nf(e){return e.length===1?e[0]:e}const sf=rs({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Co,setup(e,{slots:t}){const n=Cn(Co(e)),{options:s}=Ie(ds),r=_e(()=>({[Ao(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[Ao(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&nf(t.default(n));return e.custom?o:hr("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}}),rf=sf;function of(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function lf(e,t){for(const n in t){const s=t[n],r=e[n];if(typeof s=="string"){if(s!==r)return!1}else if(!$e(r)||r.length!==s.length||s.some((o,i)=>o!==r[i]))return!1}return!0}function xo(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Ao=(e,t,n)=>e??t??n,cf=rs({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=Ie(qs),r=_e(()=>e.route||s.value),o=Ie(wo,0),i=_e(()=>{let u=Ne(o);const{matched:a}=r.value;let f;for(;(f=a[u])&&!f.components;)u++;return u}),l=_e(()=>r.value.matched[i.value]);Fn(wo,_e(()=>i.value+1)),Fn(tf,l),Fn(qs,r);const c=It();return Wt(()=>[c.value,l.value,e.name],([u,a,f],[p,g,T])=>{a&&(a.instances[f]=u,g&&g!==a&&u&&u===p&&(a.leaveGuards.size||(a.leaveGuards=g.leaveGuards),a.updateGuards.size||(a.updateGuards=g.updateGuards))),u&&a&&(!g||!Gt(a,g)||!p)&&(a.enterCallbacks[f]||[]).forEach(E=>E(u))},{flush:"post"}),()=>{const u=r.value,a=e.name,f=l.value,p=f&&f.components[a];if(!p)return Ro(n.default,{Component:p,route:u});const g=f.props[a],T=g?g===!0?u.params:typeof g=="function"?g(u):g:null,U=hr(p,ee({},T,t,{onVnodeUnmounted:D=>{D.component.isUnmounted&&(f.instances[a]=null)},ref:c}));return Ro(n.default,{Component:U,route:u})||U}}});function Ro(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const af=cf;function uf(e){const t=Gu(e.routes,e),n=e.parseQuery||Xu,s=e.stringifyQuery||Eo,r=e.history,o=tn(),i=tn(),l=tn(),c=Yl(ut);let u=ut;$t&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const a=Cs.bind(null,y=>""+y),f=Cs.bind(null,Tu),p=Cs.bind(null,Sn);function g(y,k){let R,N;return al(y)?(R=t.getRecordMatcher(y),N=k):N=y,t.addRoute(N,R)}function T(y){const k=t.getRecordMatcher(y);k&&t.removeRoute(k)}function E(){return t.getRoutes().map(y=>y.record)}function U(y){return!!t.getRecordMatcher(y)}function D(y,k){if(k=ee({},k||c.value),typeof y=="string"){const m=xs(n,y,k.path),_=t.resolve({path:m.path},k),b=r.createHref(m.fullPath);return ee(m,_,{params:p(_.params),hash:Sn(m.hash),redirectedFrom:void 0,href:b})}let R;if(y.path!=null)R=ee({},y,{path:xs(n,y.path,k.path).path});else{const m=ee({},y.params);for(const _ in m)m[_]==null&&delete m[_];R=ee({},y,{params:f(m)}),k.params=f(k.params)}const N=t.resolve(R,k),re=y.hash||"";N.params=a(p(N.params));const d=wu(s,ee({},y,{hash:yu(re),path:N.path})),h=r.createHref(d);return ee({fullPath:d,hash:re,query:s===Eo?ef(y.query):y.query||{}},N,{redirectedFrom:void 0,href:h})}function I(y){return typeof y=="string"?xs(n,y,c.value.path):ee({},y)}function L(y,k){if(u!==y)return zt(8,{from:k,to:y})}function M(y){return Z(y)}function j(y){return M(ee(I(y),{replace:!0}))}function G(y){const k=y.matched[y.matched.length-1];if(k&&k.redirect){const{redirect:R}=k;let N=typeof R=="function"?R(y):R;return typeof N=="string"&&(N=N.includes("?")||N.includes("#")?N=I(N):{path:N},N.params={}),ee({query:y.query,hash:y.hash,params:N.path!=null?{}:y.params},N)}}function Z(y,k){const R=u=D(y),N=c.value,re=y.state,d=y.force,h=y.replace===!0,m=G(R);if(m)return Z(ee(I(m),{state:typeof m=="object"?ee({},re,m.state):re,force:d,replace:h}),k||R);const _=R;_.redirectedFrom=k;let b;return!d&&Cu(s,N,R)&&(b=zt(16,{to:_,from:N}),je(N,N,!0,!1)),(b?Promise.resolve(b):B(_,N)).catch(v=>et(v)?et(v,2)?v:ct(v):X(v,_,N)).then(v=>{if(v){if(et(v,2))return Z(ee({replace:h},I(v.to),{state:typeof v.to=="object"?ee({},re,v.to.state):re,force:d}),k||_)}else v=O(_,N,!0,h,re);return z(_,N,v),v})}function V(y,k){const R=L(y,k);return R?Promise.reject(R):Promise.resolve()}function w(y){const k=Lt.values().next().value;return k&&typeof k.runWithContext=="function"?k.runWithContext(y):y()}function B(y,k){let R;const[N,re,d]=ff(y,k);R=As(N.reverse(),"beforeRouteLeave",y,k);for(const m of N)m.leaveGuards.forEach(_=>{R.push(mt(_,y,k))});const h=V.bind(null,y,k);return R.push(h),Oe(R).then(()=>{R=[];for(const m of o.list())R.push(mt(m,y,k));return R.push(h),Oe(R)}).then(()=>{R=As(re,"beforeRouteUpdate",y,k);for(const m of re)m.updateGuards.forEach(_=>{R.push(mt(_,y,k))});return R.push(h),Oe(R)}).then(()=>{R=[];for(const m of d)if(m.beforeEnter)if($e(m.beforeEnter))for(const _ of m.beforeEnter)R.push(mt(_,y,k));else R.push(mt(m.beforeEnter,y,k));return R.push(h),Oe(R)}).then(()=>(y.matched.forEach(m=>m.enterCallbacks={}),R=As(d,"beforeRouteEnter",y,k,w),R.push(h),Oe(R))).then(()=>{R=[];for(const m of i.list())R.push(mt(m,y,k));return R.push(h),Oe(R)}).catch(m=>et(m,8)?m:Promise.reject(m))}function z(y,k,R){l.list().forEach(N=>w(()=>N(y,k,R)))}function O(y,k,R,N,re){const d=L(y,k);if(d)return d;const h=k===ut,m=$t?history.state:{};R&&(N||h?r.replace(y.fullPath,ee({scroll:h&&m&&m.scroll},re)):r.push(y.fullPath,re)),c.value=y,je(y,k,R,h),ct()}let Q;function fe(){Q||(Q=r.listen((y,k,R)=>{if(!Rn.listening)return;const N=D(y),re=G(N);if(re){Z(ee(re,{replace:!0,force:!0}),N).catch(hn);return}u=N;const d=c.value;$t&&Iu(po(d.fullPath,R.delta),fs()),B(N,d).catch(h=>et(h,12)?h:et(h,2)?(Z(ee(I(h.to),{force:!0}),N).then(m=>{et(m,20)&&!R.delta&&R.type===En.pop&&r.go(-1,!1)}).catch(hn),Promise.reject()):(R.delta&&r.go(-R.delta,!1),X(h,N,d))).then(h=>{h=h||O(N,d,!1),h&&(R.delta&&!et(h,8)?r.go(-R.delta,!1):R.type===En.pop&&et(h,20)&&r.go(-1,!1)),z(N,d,h)}).catch(hn)}))}let ve=tn(),se=tn(),W;function X(y,k,R){ct(y);const N=se.list();return N.length?N.forEach(re=>re(y,k,R)):console.error(y),Promise.reject(y)}function Je(){return W&&c.value!==ut?Promise.resolve():new Promise((y,k)=>{ve.add([y,k])})}function ct(y){return W||(W=!y,fe(),ve.list().forEach(([k,R])=>y?R(y):k()),ve.reset()),y}function je(y,k,R,N){const{scrollBehavior:re}=e;if(!$t||!re)return Promise.resolve();const d=!R&&Du(po(y.fullPath,0))||(N||!R)&&history.state&&history.state.scroll||null;return ss().then(()=>re(y,k,d)).then(h=>h&&ku(h)).catch(h=>X(h,y,k))}const we=y=>r.go(y);let Dt;const Lt=new Set,Rn={currentRoute:c,listening:!0,addRoute:g,removeRoute:T,clearRoutes:t.clearRoutes,hasRoute:U,getRoutes:E,resolve:D,options:e,push:M,replace:j,go:we,back:()=>we(-1),forward:()=>we(1),beforeEach:o.add,beforeResolve:i.add,afterEach:l.add,onError:se.add,isReady:Je,install(y){const k=this;y.component("RouterLink",rf),y.component("RouterView",af),y.config.globalProperties.$router=k,Object.defineProperty(y.config.globalProperties,"$route",{enumerable:!0,get:()=>Ne(c)}),$t&&!Dt&&c.value===ut&&(Dt=!0,M(r.location).catch(re=>{}));const R={};for(const re in ut)Object.defineProperty(R,re,{get:()=>c.value[re],enumerable:!0});y.provide(ds,k),y.provide(gr,ei(R)),y.provide(qs,c);const N=y.unmount;Lt.add(y),y.unmount=function(){Lt.delete(y),Lt.size<1&&(u=ut,Q&&Q(),Q=null,c.value=ut,Dt=!1,W=!1),N()}}};function Oe(y){return y.reduce((k,R)=>k.then(()=>w(R)),Promise.resolve())}return Rn}function ff(e,t){const n=[],s=[],r=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const l=t.matched[i];l&&(e.matched.find(u=>Gt(u,l))?s.push(l):n.push(l));const c=e.matched[i];c&&(t.matched.find(u=>Gt(u,c))||r.push(c))}return[n,s,r]}function Nf(){return Ie(ds)}function df(e){return Ie(gr)}const Rs=(e=500)=>new Promise(t=>setTimeout(t,e)),hf=()=>{const e=Date.now().toString().slice(-6),t=Math.floor(Math.random()*1e3).toString().padStart(3,"0");return`P${e}${t}`},ft=[{id:"user_001",patientNumber:"P123456001",phone:"13800138000",name:"张三",education:"本科",gender:"male",contactPhone:"13800138000",dominantHand:"right",isColorBlind:!1,idCard:"",avatar:"/avatars/default-male.png",createdAt:"2024-01-15T10:30:00Z",lastLoginAt:"2024-01-20T14:22:00Z"},{id:"user_002",patientNumber:"P123456002",phone:"13800138001",name:"李四",education:"硕士",gender:"female",contactPhone:"13800138001",dominantHand:"right",isColorBlind:!1,idCard:"110101199002022345",avatar:"/avatars/default-female.png",createdAt:"2024-01-16T09:15:00Z",lastLoginAt:"2024-01-19T16:45:00Z"},{id:"user_003",patientNumber:"P123456003",phone:"13800138002",name:"王五",education:"高中",gender:"male",contactPhone:"13800138002",dominantHand:"left",isColorBlind:!0,idCard:"110101199003033456",avatar:"/avatars/default-male.png",createdAt:"2024-01-16T11:20:00Z",lastLoginAt:"2024-01-18T13:10:00Z"}],Po=[{id:"test_001",userId:"user_001",testType:"PDQ5",testName:"PDQ-5 量表测试",startTime:"2024-01-20T10:00:00Z",endTime:"2024-01-20T10:15:00Z",status:"completed",score:85,duration:900,results:{memoryScore:85,attentionScore:82,thinkingScore:87,overallPercentile:78,recommendations:["建议加强记忆训练","保持良好的注意力习惯"]},isRetesting:!1},{id:"test_001_retest1",userId:"user_001",testType:"PDQ5",testName:"PDQ-5 量表测试",startTime:"2024-01-25T14:30:00Z",endTime:"2024-01-25T14:42:00Z",status:"completed",score:89,duration:720,results:{memoryScore:88,attentionScore:85,thinkingScore:92,overallPercentile:82,recommendations:["记忆能力有所提升","继续保持良好状态"]},isRetesting:!0},{id:"test_001_retest2",userId:"user_001",testType:"PDQ5",testName:"PDQ-5 量表测试",startTime:"2024-02-01T09:15:00Z",endTime:"2024-02-01T09:25:00Z",status:"completed",score:92,duration:600,results:{memoryScore:91,attentionScore:88,thinkingScore:95,overallPercentile:85,recommendations:["认知能力持续改善","表现优秀"]},isRetesting:!0},{id:"test_002",userId:"user_001",testType:"Hopkins",testName:"Hopkins 词汇学习测试",startTime:"2024-01-19T14:30:00Z",endTime:"2024-01-19T14:50:00Z",status:"completed",score:78,duration:1200,results:{immediateRecall:80,delayedRecall:76,learningEfficiency:85,retentionRate:73,overallPercentile:65,recommendations:["建议进行重复学习练习","加强长期记忆巩固"]},isRetesting:!1},{id:"test_002_retest1",userId:"user_001",testType:"Hopkins",testName:"Hopkins 词汇学习测试",startTime:"2024-01-26T16:00:00Z",endTime:"2024-01-26T16:18:00Z",status:"completed",score:82,duration:1080,results:{immediateRecall:84,delayedRecall:80,learningEfficiency:88,retentionRate:76,overallPercentile:70,recommendations:["记忆能力有所提升","继续练习延迟回忆"]},isRetesting:!0},{id:"test_003",userId:"user_001",testType:"Stroop",testName:"Stroop 色词测试",startTime:"2024-01-21T16:00:00Z",endTime:"2024-01-21T16:08:00Z",status:"completed",score:92,duration:480,results:{reactionTime:650,accuracy:94,interferenceControl:88,overallPercentile:82,recommendations:["优秀的抑制控制能力","继续保持专注训练"]},isRetesting:!1},{id:"test_003_retest1",userId:"user_001",testType:"Stroop",testName:"Stroop 色词测试",startTime:"2024-01-28T10:30:00Z",endTime:"2024-01-28T10:37:00Z",status:"completed",score:95,duration:420,results:{reactionTime:580,accuracy:96,interferenceControl:92,overallPercentile:88,recommendations:["反应时间显著改善","抑制控制能力优秀"]},isRetesting:!0},{id:"test_004",userId:"user_001",testType:"NBack",testName:"N-back工作记忆测试",startTime:"2024-01-18T09:15:00Z",endTime:"2024-01-18T09:40:00Z",status:"completed",score:88,duration:1500,results:{forwardSpan:7,backwardSpan:6,workingMemoryCapacity:85,processingSpeed:82,overallPercentile:75,recommendations:["工作记忆表现良好","可适当增加复杂任务练习"]},isRetesting:!1},{id:"test_004_retest1",userId:"user_001",testType:"NBack",testName:"N-back工作记忆测试",startTime:"2024-01-24T15:20:00Z",endTime:"2024-01-24T15:42:00Z",status:"completed",score:91,duration:1320,results:{forwardSpan:8,backwardSpan:7,workingMemoryCapacity:88,processingSpeed:85,overallPercentile:80,recommendations:["工作记忆能力提升明显","继续保持训练强度"]},isRetesting:!0},{id:"test_005",userId:"user_001",testType:"TrailMaking",testName:"连线测试",startTime:"2024-01-22T11:00:00Z",endTime:"2024-01-22T11:12:00Z",status:"completed",score:86,duration:720,results:{trailATime:45,trailBTime:95,executiveFunction:84,processingSpeed:88,overallPercentile:72,recommendations:["执行功能表现良好","可进行更复杂的认知训练"]},isRetesting:!1},{id:"test_005_retest1",userId:"user_001",testType:"TrailMaking",testName:"连线测试",startTime:"2024-01-29T14:15:00Z",endTime:"2024-01-29T14:25:00Z",status:"completed",score:90,duration:600,results:{trailATime:38,trailBTime:82,executiveFunction:89,processingSpeed:92,overallPercentile:78,recommendations:["执行功能持续改善","处理速度显著提升"]},isRetesting:!0},{id:"test_006",userId:"user_001",testType:"VerbalFluency",testName:"词语流畅性测试",startTime:"2024-01-23T13:30:00Z",endTime:"2024-01-23T13:45:00Z",status:"completed",score:83,duration:900,results:{categoryFluency:85,letterFluency:81,semanticMemory:84,executiveControl:82,overallPercentile:68,recommendations:["语义流畅性表现良好","可加强字母流畅性练习"]},isRetesting:!1},{id:"test_007",userId:"user_001",testType:"CPT",testName:"持续性操作测试",startTime:"2024-01-17T10:45:00Z",endTime:"2024-01-17T11:00:00Z",status:"completed",score:87,duration:900,results:{attentionSpan:89,vigilance:85,responseConsistency:88,omissionErrors:3,overallPercentile:74,recommendations:["注意力持续性良好","反应一致性优秀"]},isRetesting:!1},{id:"test_008",userId:"user_001",testType:"DSST",testName:"DSST数字符号转换测试",startTime:"2024-01-16T16:20:00Z",endTime:"2024-01-16T16:35:00Z",status:"completed",score:79,duration:900,results:{processingSpeed:82,workingMemory:76,visualMotorCoordination:80,learningEfficiency:78,overallPercentile:62,recommendations:["处理速度中等","建议加强视觉运动协调训练"]},isRetesting:!1}],hl=e=>{const t=Po.filter(n=>n.userId===e);return t.length===0?Po.filter(n=>n.userId==="user_001").map(n=>({...n,userId:e})):t},Ff=e=>{const t=hl(e),n={};t.forEach(o=>{const i=o.startTime.split("T")[0];n[i]||(n[i]=[]),n[i].push(o)});const s=Object.keys(n).sort((o,i)=>i.localeCompare(o)),r={};return s.forEach(o=>{r[o]=n[o].sort((i,l)=>new Date(l.startTime).getTime()-new Date(i.startTime).getTime())}),r},Hf=(e,t)=>hl(e).filter(s=>s.testType===t).sort((s,r)=>new Date(r.startTime).getTime()-new Date(s.startTime).getTime()),$f=e=>!e.results||e.status!=="completed"?null:{id:e.id,testName:e.testName,testType:e.testType,completedAt:e.endTime,duration:e.duration,score:e.score,percentile:e.results.overallPercentile||0,detailedResults:e.results,recommendations:e.results.recommendations||[],interpretation:pf(e.score||0),comparisonData:gf(e.testType,e.score||0)},pf=e=>e>=90?"优秀 - 表现非常出色":e>=80?"良好 - 表现高于平均水平":e>=70?"中等 - 表现处于平均水平":e>=60?"偏低 - 建议加强训练":"需要改善 - 建议寻求专业指导",gf=(e,t)=>{const s={PDQ5:75,Hopkins:72,Stroop:78,NBack:70,TrailMaking:74,VerbalFluency:76,CPT:73,DSST:77}[e]||75,r=t-s;return{userScore:t,averageScore:s,difference:r,percentageDiff:Math.round(r/s*100),isAboveAverage:r>0}},jf=[{label:"小学",value:"小学"},{label:"初中",value:"初中"},{label:"高中",value:"高中"},{label:"大专",value:"大专"},{label:"本科",value:"本科"},{label:"硕士",value:"硕士"},{label:"博士",value:"博士"}],pl=[{id:"task_pdq5",type:"PDQ5",name:"PDQ-5量表测试",description:"评估记忆力、注意力、思维反应和认知灵活性",briefDescription:"基础认知能力自评量表",fullDescription:"通过自我评估的方式，测量您在日常生活中的记忆力、注意力、思维反应速度和认知灵活性表现。",instructions:["请根据最近一个月的实际情况回答","每个问题都有5个选项，请选择最符合您情况的答案","请诚实作答，这将有助于准确评估您的认知状态"],estimatedDuration:15,difficulty:"easy",category:"认知评估",icon:"🧠",isAvailable:!0,isMainTask:!0,order:1},{id:"task_hopkins",type:"Hopkins",name:"Hopkins词汇学习测试",description:"测试即时记忆和延迟记忆能力",briefDescription:"语义记忆和学习能力测试",fullDescription:"通过学习和回忆词汇列表，评估您的语义记忆、学习能力和记忆保持能力。包括即时回忆和延迟回忆两个阶段。",instructions:["首先您需要学习一系列词汇","然后立即进行回忆测试","经过一段时间后再次进行延迟回忆测试","请尽您所能地记住学习的内容"],estimatedDuration:20,difficulty:"medium",category:"记忆测试",icon:"📚",isAvailable:!0,isMainTask:!0,order:2},{id:"task_nback",type:"NBack",name:"N-back工作记忆测试",description:"评估工作记忆和注意力持续性",estimatedDuration:25,difficulty:"medium",category:"记忆测试",icon:"🔢",isAvailable:!0,isMainTask:!0,order:3},{id:"task_stroop",type:"Stroop",name:"Stroop色词测试",description:"测试抑制控制能力和认知灵活性",briefDescription:"注意力和抑制控制能力测试",fullDescription:"测量您在面对冲突信息时的注意力集中能力和抑制不相关反应的能力。您需要快速准确地识别颜色名称的字体颜色。",instructions:["看到颜色名称后，请判断字体的颜色而不是字的意思","尽可能快速而准确地做出回应","注意不要被字的含义干扰","测试分为练习和正式测试两部分"],estimatedDuration:10,difficulty:"hard",category:"注意力测试",icon:"🎨",isAvailable:!0,isMainTask:!0,order:4},{id:"task_trail",type:"TrailMaking",name:"连线测试",description:"评估执行功能和视觉-运动协调",estimatedDuration:12,difficulty:"medium",category:"执行功能",icon:"🔗",isAvailable:!0,isMainTask:!0,order:5},{id:"task_fluency",type:"VerbalFluency",name:"词语流畅性测试",description:"评估语言功能和语义记忆",estimatedDuration:8,difficulty:"easy",category:"语言测试",icon:"💬",isAvailable:!0,isMainTask:!0,order:6},{id:"task_cpt",type:"CPT",name:"持续性操作测试",description:"测试注意力持续性和反应抑制",estimatedDuration:18,difficulty:"hard",category:"注意力测试",icon:"⏱️",isAvailable:!0,isMainTask:!0,order:7},{id:"task_dsst",type:"DSST",name:"DSST数字符号转换",description:"评估处理速度和视觉-运动协调",estimatedDuration:15,difficulty:"medium",category:"处理速度",icon:"🔢",isAvailable:!0,isMainTask:!0,order:8}],mf=[{id:"main_cognitive_suite",name:"认知能力综合测试",description:"完整的认知能力评估，包含所有核心测试项目",icon:"🧠",tasks:["task_pdq5","task_hopkins","task_nback","task_stroop","task_trail","task_fluency","task_cpt","task_dsst"],estimatedDuration:123,isCompleted:!1,completionRate:0,suiteNumber:"CS-2024-001",createdDate:"2024-01-15",status:"not_started"}],gl=()=>pl.filter(e=>e.isMainTask&&e.isAvailable).sort((e,t)=>e.order-t.order),Vf=e=>{const t=gl();return t.every(s=>e.includes(s.id))?pl.filter(s=>s.isAvailable):t},Bf=(e,t)=>{const n=mf.find(r=>r.id===e);if(!n)return 0;const s=n.tasks.filter(r=>t.includes(r)).length;return Math.round(s/n.tasks.length*100)},Uf=e=>gl().every(n=>e.includes(n.id)),ml=el("auth",()=>{const e=It(null),t=It(""),n=_e(()=>!!e.value&&!!t.value),s=async c=>{await Rs();let u;switch(c.type){case"phone":u=ft.find(a=>a.phone===c.value);break;case"patientNumber":u=ft.find(a=>a.patientNumber===c.value);break;case"qrcode":u=ft.find(a=>a.patientNumber===c.value);break}return u?(e.value=u,t.value=`mock_token_${u.id}`,localStorage.setItem("user",JSON.stringify(u)),localStorage.setItem("token",t.value),u.lastLoginAt=new Date().toISOString(),{success:!0,message:"登录成功"}):{success:!1,message:"用户不存在或密码错误"}},r=async c=>{if(await Rs(),c.phone&&ft.some(f=>f.phone===c.phone))return{success:!1,message:"手机号已被使用"};const u=hf(),a={id:`user_${Date.now()}`,patientNumber:u,phone:c.phone,name:c.name,education:c.education,gender:c.gender,contactPhone:c.contactPhone,dominantHand:c.dominantHand,isColorBlind:c.isColorBlind,testLocation:c.testLocation,createdAt:new Date().toISOString(),lastLoginAt:new Date().toISOString()};return ft.push(a),e.value=a,t.value=`mock_token_${a.id}`,localStorage.setItem("user",JSON.stringify(a)),localStorage.setItem("token",t.value),{success:!0,message:"注册成功",patientNumber:u}},o=()=>{e.value=null,t.value="",localStorage.removeItem("user"),localStorage.removeItem("token")};return{user:e,token:t,isLoggedIn:n,login:s,register:r,logout:o,restoreAuth:()=>{const c=localStorage.getItem("user"),u=localStorage.getItem("token");if(c&&u)try{e.value=JSON.parse(c),t.value=u}catch(a){console.error("Failed to restore auth state:",a),o()}},updateProfile:async c=>{if(await Rs(),e.value){e.value={...e.value,...c},localStorage.setItem("user",JSON.stringify(e.value));const u=ft.findIndex(a=>a.id===e.value.id);return u!==-1&&(ft[u]={...ft[u],...c}),{success:!0,message:"更新成功"}}return{success:!1,message:"用户未登录"}}}}),_l=uf({history:Hu(),routes:[{path:"/",redirect:"/login"},{path:"/login",name:"Login",component:()=>qe(()=>import("./LoginView-BbfaX9i7.js"),__vite__mapDeps([0,1])),meta:{requiresAuth:!1}},{path:"/register",name:"Register",component:()=>qe(()=>import("./RegisterView-Du8vy0Gv.js"),__vite__mapDeps([2,3])),meta:{requiresAuth:!1}},{path:"/home",name:"Home",component:()=>qe(()=>import("./HomeView-BoU89C5E.js"),__vite__mapDeps([4,5,6])),meta:{requiresAuth:!0}},{path:"/profile",name:"Profile",component:()=>qe(()=>import("./ProfileView-DBNjcqBv.js"),__vite__mapDeps([7,5,8])),meta:{requiresAuth:!0}},{path:"/test-history",name:"TestHistory",component:()=>qe(()=>import("./TestHistoryView-DO6lqmN_.js"),__vite__mapDeps([9,5,10])),meta:{requiresAuth:!0}},{path:"/tests",name:"Tests",component:()=>qe(()=>import("./TestListView-nUrNDqvm.js"),__vite__mapDeps([11,12])),meta:{requiresAuth:!0}},{path:"/cognitive-assessment",name:"CognitiveAssessment",component:()=>qe(()=>import("./CognitiveAssessmentView-D5kvBd1Z.js"),__vite__mapDeps([13,14,15,16])),meta:{requiresAuth:!0}},{path:"/tests/pdq5/instructions",name:"PDQ5Instructions",component:()=>qe(()=>import("./PDQ5InstructionsView-xWDbLr1e.js"),__vite__mapDeps([17,18,14,15,19])),meta:{requiresAuth:!0}},{path:"/tests/pdq5",name:"PDQ5Test",component:()=>qe(()=>import("./PDQ5TestView-DAt79azD.js"),__vite__mapDeps([20,18,14,15,21])),meta:{requiresAuth:!0}},{path:"/tests/masked-emotion",name:"MaskedEmotionTest",component:()=>qe(()=>import("./MaskedEmotionTestView-BXiev3xG.js"),__vite__mapDeps([22,14,15,23])),meta:{requiresAuth:!0}}]});_l.beforeEach((e,t,n)=>{const s=ml();e.meta.requiresAuth&&!s.isLoggedIn?n("/login"):(e.name==="Login"||e.name==="Register")&&s.isLoggedIn?n("/home"):n()});const yl=el("theme",()=>{const e=It("system"),t=It(!1),n=_e(()=>e.value==="system"?t.value:e.value==="dark"),s=_e(()=>n.value?"dark":"light"),r=()=>{if(typeof window<"u"&&window.matchMedia){const a=window.matchMedia("(prefers-color-scheme: dark)");t.value=a.matches,a.addEventListener("change",f=>{t.value=f.matches})}},o=a=>{if(typeof document<"u"){const f=document.documentElement;f.classList.remove("light","dark"),f.classList.add(a),f.setAttribute("data-theme",a)}},i=a=>{e.value=a,localStorage.setItem("theme-mode",a)},l=()=>{e.value==="light"?i("dark"):e.value==="dark"?i("light"):i(t.value?"light":"dark")},c=()=>{const a=localStorage.getItem("theme-mode");a&&["light","dark","system"].includes(a)&&(e.value=a)},u=()=>{r(),c()};return Wt(n,a=>{o(a?"dark":"light")},{immediate:!0}),{mode:e,isDark:n,currentTheme:s,systemPrefersDark:t,setMode:i,toggleTheme:l,initTheme:u,applyTheme:o}}),_f=["aria-label"],yf={class:"theme-toggle__icon-container"},vf={key:0,class:"theme-toggle__label"},bf=rs({__name:"ThemeToggle",props:{showLabel:{type:Boolean,default:!1},size:{default:"medium"}},setup(e){const t=yl(),{isDark:n,toggleTheme:s}=t,r=()=>{s()};return(o,i)=>(Pt(),jt("button",{onClick:r,class:Kt(["theme-toggle",{"theme-toggle--dark":Ne(n)}]),"aria-label":Ne(n)?"切换到明亮模式":"切换到暗黑模式",type:"button"},[Vt("div",yf,[(Pt(),jt("svg",{class:Kt(["theme-toggle__icon theme-toggle__icon--sun",{"theme-toggle__icon--active":!Ne(n)}]),width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},i[0]||(i[0]=[Vt("circle",{cx:"12",cy:"12",r:"4",stroke:"currentColor","stroke-width":"2"},null,-1),Vt("path",{d:"M12 2v2M12 20v2M4.93 4.93l1.41 1.41M17.66 17.66l1.41 1.41M2 12h2M20 12h2M6.34 6.34L4.93 4.93M19.07 19.07l-1.41-1.41",stroke:"currentColor","stroke-width":"2"},null,-1)]),2)),(Pt(),jt("svg",{class:Kt(["theme-toggle__icon theme-toggle__icon--moon",{"theme-toggle__icon--active":Ne(n)}]),width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},i[1]||(i[1]=[Vt("path",{d:"M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z",stroke:"currentColor","stroke-width":"2",fill:"currentColor"},null,-1)]),2))]),o.showLabel?(Pt(),jt("span",vf,Lo(Ne(n)?"暗黑模式":"明亮模式"),1)):Ui("",!0)],10,_f))}}),Tf=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},Sf=Tf(bf,[["__scopeId","data-v-e74a5b2b"]]),Ef={id:"app",class:"app-container"},wf={key:0,class:"theme-toggle-wrapper"},Cf=rs({__name:"App",setup(e){const t=yl(),n=ml(),s=df(),r=_e(()=>{var o,i;return s.path.includes("/pdq5")||((o=s.name)==null?void 0:o.toString().toLowerCase().includes("pdq5"))||s.path.includes("/masked-emotion-test")||((i=s.name)==null?void 0:i.toString().toLowerCase().includes("maskedemotiontest"))});return lr(()=>{t.initTheme(),n.restoreAuth()}),(o,i)=>{const l=xc("router-view");return Pt(),jt("div",Ef,[r.value?Ui("",!0):(Pt(),jt("div",wf,[ye(Sf)])),ye(l)])}}}),mr=Ja(Cf);mr.use(eu());mr.use(_l);mr.mount("#app");export{Vf as $,ro as A,Ne as B,jf as C,Ua as D,Ba as E,Ze as F,_c as G,ue as H,Wt as I,mc as J,Cn as K,dr as L,Ie as M,la as N,bi as O,Af as P,kf as Q,Fn as R,Ja as S,Mf as T,gl as U,mf as V,hl as W,Ff as X,Uf as Y,Bf as Z,Tf as _,Nf as a,$f as a0,pl as a1,Hf as a2,Ka as a3,Rf as a4,hr as a5,Sf as a6,jt as b,_e as c,rs as d,Vt as e,Ui as f,ia as g,ye as h,fc as i,xc as j,Df as k,Pt as l,Ti as m,Kt as n,lr as o,Lf as p,ss as q,It as r,Qs as s,Lo as t,ml as u,If as v,xf as w,Pf as x,Of as y,sa as z};
