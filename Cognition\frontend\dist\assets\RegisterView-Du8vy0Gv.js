import{d as A,r as u,c as L,o as K,m as X,b as v,n as _,e,h as $,p as V,k as y,t as q,T as j,i as z,q as G,f as b,s as J,F as Q,x as W,l as n,_ as I,y as D,u as Y,z as N,j as Z,w as x,A as H,B as ee,C as ae,D as T,E as te,g as O,a as de}from"./index-tHlaP7TV.js";const le=["onKeydown","aria-expanded"],se={class:"dropdown-content"},ie=["onClick","onMouseenter","aria-selected"],oe=A({__name:"CustomSelect",props:{modelValue:{},options:{},placeholder:{default:"请选择"},disabled:{type:Boolean,default:!1},maxHeight:{default:"200px"}},emits:["update:modelValue","change"],setup(B,{emit:c}){const d=B,r=c,i=u(!1),p=u(-1),C=L(()=>d.options.find(l=>l.value===d.modelValue)),w=L(()=>({maxHeight:d.maxHeight})),g=()=>{d.disabled||(i.value=!i.value,i.value&&(p.value=d.options.findIndex(l=>l.value===d.modelValue),G(()=>{t()})))},f=()=>{i.value=!1,p.value=-1},M=l=>{r("update:modelValue",l.value),r("change",l.value),f()},P=l=>{if(!i.value){g();return}const o=p.value+l;o>=0&&o<d.options.length&&(p.value=o,t())},t=()=>{const l=document.querySelector(".dropdown-content"),o=document.querySelector(".dropdown-option.is-highlighted");l&&o&&o.scrollIntoView({block:"nearest"})},k=l=>{l.target.closest(".custom-select")||f()};return K(()=>{document.addEventListener("click",k)}),X(()=>{document.removeEventListener("click",k)}),(l,o)=>(n(),v("div",{class:_(["custom-select",{"is-open":i.value,"is-disabled":l.disabled}])},[e("div",{class:"select-trigger",onClick:g,onKeydown:[V(y(g,["prevent"]),["enter"]),V(y(g,["prevent"]),["space"]),V(f,["escape"]),o[0]||(o[0]=V(y(m=>P(1),["prevent"]),["arrow-down"])),o[1]||(o[1]=V(y(m=>P(-1),["prevent"]),["arrow-up"]))],tabindex:"0","aria-expanded":i.value,"aria-haspopup":!0,role:"combobox"},[e("span",{class:_(["selected-text",{placeholder:!C.value}])},q(C.value?C.value.label:l.placeholder),3),(n(),v("svg",{class:_(["dropdown-icon",{rotated:i.value}]),width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},o[2]||(o[2]=[e("polyline",{points:"6,9 12,15 18,9"},null,-1)]),2))],40,le),$(j,{name:"dropdown"},{default:z(()=>[i.value?(n(),v("div",{key:0,class:"dropdown-menu",style:J(w.value)},[e("div",se,[(n(!0),v(Q,null,W(l.options,(m,U)=>(n(),v("div",{key:m.value,class:_(["dropdown-option",{"is-selected":m.value===l.modelValue,"is-highlighted":U===p.value}]),onClick:S=>M(m),onMouseenter:S=>p.value=U,role:"option","aria-selected":m.value===l.modelValue},q(m.label),43,ie))),128))])],4)):b("",!0)]),_:1})],2))}}),R=I(oe,[["__scopeId","data-v-83049ec6"]]),ne={class:"agreement-content"},re={class:"agreement-header"},ve=A({__name:"UserAgreement",emits:["close","agree"],setup(B){return(c,d)=>(n(),v("div",{class:"agreement-modal",onClick:d[1]||(d[1]=y(r=>c.$emit("close"),["self"]))},[e("div",ne,[e("div",re,[d[3]||(d[3]=e("h2",{class:"agreement-title"},"用户协议",-1)),e("button",{onClick:d[0]||(d[0]=r=>c.$emit("close")),class:"close-button","aria-label":"关闭"},d[2]||(d[2]=[e("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[e("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),e("line",{x1:"6",y1:"6",x2:"18",y2:"18"})],-1)]))]),d[4]||(d[4]=D('<div class="agreement-body" data-v-37c178ad><div class="agreement-section" data-v-37c178ad><h3 data-v-37c178ad>1. 服务条款的接受</h3><p data-v-37c178ad>欢迎使用认知测试系统（以下简称&quot;本系统&quot;）。在使用本系统提供的服务之前，请您仔细阅读并充分理解本用户协议（以下简称&quot;本协议&quot;）的各项条款。您的注册、登录、使用等行为将视为对本协议的接受，并同意接受本协议各项条款的约束。</p></div><div class="agreement-section" data-v-37c178ad><h3 data-v-37c178ad>2. 服务内容</h3><p data-v-37c178ad>本系统为用户提供认知能力测试服务，包括但不限于：</p><ul data-v-37c178ad><li data-v-37c178ad>心理健康量表测评</li><li data-v-37c178ad>认知任务分析</li><li data-v-37c178ad>生理信号采集</li><li data-v-37c178ad>抑郁情绪识别</li><li data-v-37c178ad>精神障碍风险筛查</li><li data-v-37c178ad>个性化测试报告生成</li></ul></div><div class="agreement-section" data-v-37c178ad><h3 data-v-37c178ad>3. 用户注册与账户</h3><p data-v-37c178ad>3.1 用户需要提供真实、准确、完整的个人信息进行注册。</p><p data-v-37c178ad>3.2 用户有义务及时更新注册信息，确保信息的真实性和有效性。</p><p data-v-37c178ad>3.3 用户应当妥善保管账户信息，不得将账户借给他人使用。</p><p data-v-37c178ad>3.4 如发现账户被盗用或存在安全漏洞，应立即通知我们。</p></div><div class="agreement-section" data-v-37c178ad><h3 data-v-37c178ad>4. 用户行为规范</h3><p data-v-37c178ad>用户在使用本系统时，应当遵守以下规范：</p><ul data-v-37c178ad><li data-v-37c178ad>遵守国家法律法规和社会公德</li><li data-v-37c178ad>不得利用本系统从事违法违规活动</li><li data-v-37c178ad>不得干扰或破坏系统的正常运行</li><li data-v-37c178ad>不得恶意攻击系统或盗取他人信息</li><li data-v-37c178ad>诚实完成测试，不得故意提供虚假信息</li></ul></div><div class="agreement-section" data-v-37c178ad><h3 data-v-37c178ad>5. 知识产权</h3><p data-v-37c178ad>5.1 本系统的所有内容，包括但不限于文字、图片、音频、视频、软件、程序、版面设计等均受知识产权法保护。</p><p data-v-37c178ad>5.2 未经授权，用户不得复制、传播、展示、镜像、上传、下载本系统的任何内容。</p><p data-v-37c178ad>5.3 用户在使用过程中产生的测试数据，我们享有分析和研究的权利（在匿名化处理后）。</p></div><div class="agreement-section" data-v-37c178ad><h3 data-v-37c178ad>6. 免责声明</h3><p data-v-37c178ad>6.1 本系统提供的测试结果仅供参考，不能替代专业医疗诊断。</p><p data-v-37c178ad>6.2 用户应当理性对待测试结果，如有疑虑应咨询专业医疗机构。</p><p data-v-37c178ad>6.3 我们不对因用户使用本系统而产生的任何直接或间接损失承担责任。</p><p data-v-37c178ad>6.4 因不可抗力因素导致的服务中断或数据丢失，我们不承担责任。</p></div><div class="agreement-section" data-v-37c178ad><h3 data-v-37c178ad>7. 服务变更与终止</h3><p data-v-37c178ad>7.1 我们有权根据业务需要修改或终止服务内容。</p><p data-v-37c178ad>7.2 如需终止服务，我们将提前30天通知用户。</p><p data-v-37c178ad>7.3 用户违反本协议的，我们有权暂停或终止其账户。</p></div><div class="agreement-section" data-v-37c178ad><h3 data-v-37c178ad>8. 协议修改</h3><p data-v-37c178ad>我们有权根据法律法规变化或业务需要修改本协议。修改后的协议将在系统内公布，用户继续使用服务即视为接受修改后的协议。</p></div><div class="agreement-section" data-v-37c178ad><h3 data-v-37c178ad>9. 争议解决</h3><p data-v-37c178ad>因本协议产生的争议，双方应友好协商解决。协商不成的，提交有管辖权的人民法院解决。</p></div><div class="agreement-section" data-v-37c178ad><h3 data-v-37c178ad>10. 其他条款</h3><p data-v-37c178ad>10.1 本协议的解释权归我们所有。</p><p data-v-37c178ad>10.2 如本协议的任何条款被认定为无效，其余条款仍然有效。</p><p data-v-37c178ad>10.3 本协议自用户接受之日起生效。</p></div></div>',1))])]))}}),ue=I(ve,[["__scopeId","data-v-37c178ad"]]),ce={class:"privacy-content"},pe={class:"privacy-header"},ge=A({__name:"PrivacyPolicy",emits:["close","agree"],setup(B){return(c,d)=>(n(),v("div",{class:"privacy-modal",onClick:d[1]||(d[1]=y(r=>c.$emit("close"),["self"]))},[e("div",ce,[e("div",pe,[d[3]||(d[3]=e("h2",{class:"privacy-title"},"隐私政策",-1)),e("button",{onClick:d[0]||(d[0]=r=>c.$emit("close")),class:"close-button","aria-label":"关闭"},d[2]||(d[2]=[e("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2"},[e("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),e("line",{x1:"6",y1:"6",x2:"18",y2:"18"})],-1)]))]),d[4]||(d[4]=D('<div class="privacy-body" data-v-1ad811ee><div class="privacy-section" data-v-1ad811ee><h3 data-v-1ad811ee>引言</h3><p data-v-1ad811ee>我们深知个人信息对您的重要性，并会尽全力保护您的个人信息安全可靠。我们致力于维持您对我们的信任，恪守以下原则，保护您的个人信息：权责一致原则、目的明确原则、选择同意原则、最少够用原则、确保安全原则、主体参与原则、公开透明原则等。</p></div><div class="privacy-section" data-v-1ad811ee><h3 data-v-1ad811ee>1. 我们收集的信息</h3><h4 data-v-1ad811ee>1.1 您主动提供的信息</h4><ul data-v-1ad811ee><li data-v-1ad811ee><strong data-v-1ad811ee>注册信息：</strong>姓名、性别、年龄、学历、联系电话、身份证号等</li><li data-v-1ad811ee><strong data-v-1ad811ee>测试信息：</strong>惯用手、色盲情况等个人特征信息</li><li data-v-1ad811ee><strong data-v-1ad811ee>反馈信息：</strong>您通过客服或反馈功能提供的信息</li></ul><h4 data-v-1ad811ee>1.2 我们自动收集的信息</h4><ul data-v-1ad811ee><li data-v-1ad811ee><strong data-v-1ad811ee>测试数据：</strong>测试过程中的反应时间、准确率、操作轨迹等</li><li data-v-1ad811ee><strong data-v-1ad811ee>设备信息：</strong>设备型号、操作系统、浏览器类型等</li><li data-v-1ad811ee><strong data-v-1ad811ee>日志信息：</strong>访问时间、IP地址、访问页面等</li><li data-v-1ad811ee><strong data-v-1ad811ee>位置信息：</strong>基于IP地址的大致地理位置（如需要）</li></ul></div><div class="privacy-section" data-v-1ad811ee><h3 data-v-1ad811ee>2. 信息使用目的</h3><p data-v-1ad811ee>我们收集和使用您的个人信息，主要用于以下目的：</p><ul data-v-1ad811ee><li data-v-1ad811ee>提供认知测试服务和生成个性化报告</li><li data-v-1ad811ee>验证用户身份，保障账户安全</li><li data-v-1ad811ee>改进和优化我们的服务质量</li><li data-v-1ad811ee>进行数据分析和科学研究（匿名化处理）</li><li data-v-1ad811ee>遵守法律法规要求</li><li data-v-1ad811ee>与您沟通服务相关事宜</li></ul></div><div class="privacy-section" data-v-1ad811ee><h3 data-v-1ad811ee>3. 信息共享与披露</h3><p data-v-1ad811ee>我们不会向第三方出售、出租或以其他方式披露您的个人信息，除非：</p><ul data-v-1ad811ee><li data-v-1ad811ee>获得您的明确同意</li><li data-v-1ad811ee>法律法规要求或政府部门要求</li><li data-v-1ad811ee>为保护我们或他人的合法权益</li><li data-v-1ad811ee>与合作伙伴共享匿名化的统计数据</li><li data-v-1ad811ee>在企业重组、合并或收购时转移数据</li></ul><p data-v-1ad811ee><strong data-v-1ad811ee>合作伙伴：</strong>我们可能与以下类型的合作伙伴共享信息：</p><ul data-v-1ad811ee><li data-v-1ad811ee>技术服务提供商（云存储、数据分析等）</li><li data-v-1ad811ee>医疗机构或研究机构（匿名化数据）</li><li data-v-1ad811ee>法律顾问或审计机构</li></ul></div><div class="privacy-section" data-v-1ad811ee><h3 data-v-1ad811ee>4. 信息存储与安全</h3><h4 data-v-1ad811ee>4.1 存储地点</h4><p data-v-1ad811ee>您的个人信息将存储在中华人民共和国境内。如需跨境传输，我们将严格按照法律法规要求执行。</p><h4 data-v-1ad811ee>4.2 存储期限</h4><ul data-v-1ad811ee><li data-v-1ad811ee>账户信息：账户存续期间</li><li data-v-1ad811ee>测试数据：5年（用于科学研究）</li><li data-v-1ad811ee>日志信息：6个月</li><li data-v-1ad811ee>其他信息：根据业务需要和法律要求确定</li></ul><h4 data-v-1ad811ee>4.3 安全措施</h4><ul data-v-1ad811ee><li data-v-1ad811ee>数据加密传输和存储</li><li data-v-1ad811ee>访问权限控制和身份认证</li><li data-v-1ad811ee>定期安全审计和漏洞扫描</li><li data-v-1ad811ee>员工安全培训和保密协议</li><li data-v-1ad811ee>数据备份和灾难恢复机制</li></ul></div><div class="privacy-section" data-v-1ad811ee><h3 data-v-1ad811ee>5. 您的权利</h3><p data-v-1ad811ee>根据相关法律法规，您享有以下权利：</p><ul data-v-1ad811ee><li data-v-1ad811ee><strong data-v-1ad811ee>知情权：</strong>了解我们处理您个人信息的情况</li><li data-v-1ad811ee><strong data-v-1ad811ee>访问权：</strong>查看我们收集的您的个人信息</li><li data-v-1ad811ee><strong data-v-1ad811ee>更正权：</strong>更正不准确或不完整的个人信息</li><li data-v-1ad811ee><strong data-v-1ad811ee>删除权：</strong>要求删除您的个人信息</li><li data-v-1ad811ee><strong data-v-1ad811ee>限制处理权：</strong>限制我们处理您的个人信息</li><li data-v-1ad811ee><strong data-v-1ad811ee>数据可携权：</strong>获取您的个人信息副本</li><li data-v-1ad811ee><strong data-v-1ad811ee>撤回同意权：</strong>撤回您之前给予的同意</li></ul></div><div class="privacy-section" data-v-1ad811ee><h3 data-v-1ad811ee>6. 未成年人保护</h3><p data-v-1ad811ee>我们非常重视未成年人的个人信息保护：</p><ul data-v-1ad811ee><li data-v-1ad811ee>不满14周岁的儿童使用我们的服务需要监护人同意</li><li data-v-1ad811ee>我们不会主动收集未成年人的个人信息</li><li data-v-1ad811ee>如发现收集了未成年人信息，我们将及时删除</li><li data-v-1ad811ee>监护人有权查看、更正或删除未成年人的信息</li></ul></div><div class="privacy-section" data-v-1ad811ee><h3 data-v-1ad811ee>7. Cookie和类似技术</h3><p data-v-1ad811ee>我们使用Cookie和类似技术来：</p><ul data-v-1ad811ee><li data-v-1ad811ee>记住您的登录状态和偏好设置</li><li data-v-1ad811ee>分析网站使用情况，改进用户体验</li><li data-v-1ad811ee>提供个性化的服务内容</li><li data-v-1ad811ee>防范安全风险</li></ul><p data-v-1ad811ee>您可以通过浏览器设置管理Cookie，但这可能影响某些功能的使用。</p></div><div class="privacy-section" data-v-1ad811ee><h3 data-v-1ad811ee>8. 隐私政策更新</h3><p data-v-1ad811ee>我们可能会不时更新本隐私政策。更新后的政策将在网站上公布，并通过适当方式通知您。如果更新涉及重大变更，我们将征求您的同意。</p><p data-v-1ad811ee>重大变更包括但不限于：</p><ul data-v-1ad811ee><li data-v-1ad811ee>服务模式发生重大变化</li><li data-v-1ad811ee>个人信息使用目的发生重大变化</li><li data-v-1ad811ee>个人信息共享对象发生变化</li><li data-v-1ad811ee>您参与个人信息处理方面的权利发生重大变化</li></ul></div><div class="privacy-section" data-v-1ad811ee><h3 data-v-1ad811ee>9. 联系我们</h3><p data-v-1ad811ee>如果您对本隐私政策有任何疑问、意见或建议，请通过以下方式联系我们：</p><ul data-v-1ad811ee><li data-v-1ad811ee><strong data-v-1ad811ee>邮箱：</strong><EMAIL></li><li data-v-1ad811ee><strong data-v-1ad811ee>电话：</strong>400-123-4567</li><li data-v-1ad811ee><strong data-v-1ad811ee>地址：</strong>[公司地址]</li><li data-v-1ad811ee><strong data-v-1ad811ee>邮编：</strong>[邮政编码]</li></ul><p data-v-1ad811ee>我们将在收到您的反馈后15个工作日内回复。</p></div></div>',1))])]))}}),me=I(ge,[["__scopeId","data-v-1ad811ee"]]),he={class:"register-container"},fe={class:"register-header"},be={class:"register-content"},ye={class:"register-card"},Ce={class:"form-section"},we={class:"form-row-three"},ke={class:"form-group"},xe={class:"form-group"},Ve={class:"form-group"},_e={class:"form-row-three"},$e={class:"form-group"},qe={class:"form-group"},Be={key:0,class:"error-message"},Pe={class:"form-group"},Ue={class:"form-row-three"},Ae={class:"form-group"},Ie={class:"radio-group"},Me={class:"radio-options"},Se={class:"radio-label"},He={class:"radio-label"},Oe={class:"form-section"},Re={class:"checkbox-label agreement-label"},Le=["disabled","aria-busy"],Ne={key:0,class:"loading-spinner","aria-hidden":"true"},Te={key:0,role:"alert",class:"success-message","aria-live":"polite"},ze={class:"success-content"},De={class:"success-subtitle"},Ee={key:1,role:"alert",class:"error-message","aria-live":"polite"},Fe=A({__name:"RegisterView",setup(B){const c=de(),d=Y(),r=u(!1),i=u(""),p=u(!1),C=u(""),w=u(!1),g=u(!1),f=u(!1),M=[{label:"男",value:"male"},{label:"女",value:"female"}],P=[{label:"右手",value:"right"},{label:"左手",value:"left"}],t=u({name:"",education:"",gender:"male",contactPhone:"",dominantHand:"right",isColorBlind:!1,idCard:""}),k=h=>h?/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(h):!1,l=L(()=>t.value.name&&t.value.education&&t.value.gender&&t.value.contactPhone&&t.value.dominantHand&&k(t.value.idCard)&&(t.value.isColorBlind===!0||t.value.isColorBlind===!1)&&w.value),o=async()=>{if(!l.value){i.value="请填写所有必填项并同意用户协议";return}r.value=!0,i.value="";try{const h=await d.register(t.value);h.success?(p.value=!0,C.value=h.patientNumber||"",setTimeout(()=>{c.push("/home")},3e3)):i.value=h.message}catch(h){i.value="注册失败，请稍后重试",console.error("Register error:",h)}finally{r.value=!1}},m=()=>{g.value=!0},U=()=>{f.value=!0},S=()=>{w.value=!0,g.value=!1},E=()=>{f.value=!1};return(h,a)=>{const F=Z("router-link");return n(),v("div",he,[e("header",fe,[$(F,{to:"/login",class:"back-button","aria-label":"返回登录"},{default:z(()=>a[11]||(a[11]=[e("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"})],-1)])),_:1,__:[11]}),a[12]||(a[12]=e("h1",{class:"page-title"},"用户注册",-1))]),e("main",be,[e("div",ye,[e("form",{onSubmit:y(o,["prevent"]),class:"register-form"},[e("div",Ce,[e("div",we,[e("div",ke,[a[13]||(a[13]=e("label",{for:"name",class:"label required"},"姓名",-1)),x(e("input",{id:"name","onUpdate:modelValue":a[0]||(a[0]=s=>t.value.name=s),type:"text",placeholder:"请输入真实姓名",class:"input",required:"",maxlength:"20"},null,512),[[H,t.value.name]])]),e("div",xe,[a[14]||(a[14]=e("label",{for:"gender",class:"label required"},"性别",-1)),$(R,{modelValue:t.value.gender,"onUpdate:modelValue":a[1]||(a[1]=s=>t.value.gender=s),options:M,placeholder:"请选择性别"},null,8,["modelValue"])]),e("div",Ve,[a[15]||(a[15]=e("label",{for:"education",class:"label required"},"学历",-1)),$(R,{modelValue:t.value.education,"onUpdate:modelValue":a[2]||(a[2]=s=>t.value.education=s),options:ee(ae),placeholder:"请选择学历"},null,8,["modelValue","options"])])]),e("div",_e,[e("div",$e,[a[16]||(a[16]=e("label",{for:"contactPhone",class:"label required"},"联系电话",-1)),x(e("input",{id:"contactPhone","onUpdate:modelValue":a[3]||(a[3]=s=>t.value.contactPhone=s),type:"tel",placeholder:"请输入联系电话",class:"input",required:"",pattern:"[0-9]{11}"},null,512),[[H,t.value.contactPhone]])]),e("div",qe,[a[17]||(a[17]=e("label",{for:"idCard",class:"label required"},"身份证号",-1)),x(e("input",{id:"idCard","onUpdate:modelValue":a[4]||(a[4]=s=>t.value.idCard=s),type:"text",class:_(["input",{"input-error":t.value.idCard&&!k(t.value.idCard)}]),placeholder:"请输入18位身份证号",pattern:"[0-9X]{18}",maxlength:"18",required:""},null,2),[[H,t.value.idCard]]),t.value.idCard&&!k(t.value.idCard)?(n(),v("div",Be," 请输入正确的18位身份证号格式 ")):b("",!0)]),e("div",Pe,[a[18]||(a[18]=e("label",{for:"dominantHand",class:"label required"},"惯用手",-1)),$(R,{modelValue:t.value.dominantHand,"onUpdate:modelValue":a[5]||(a[5]=s=>t.value.dominantHand=s),options:P,placeholder:"请选择惯用手"},null,8,["modelValue"])])]),e("div",Ue,[e("div",Ae,[e("fieldset",Ie,[a[21]||(a[21]=e("legend",{class:"label required"},"是否色盲",-1)),e("div",Me,[e("label",Se,[x(e("input",{type:"radio","onUpdate:modelValue":a[6]||(a[6]=s=>t.value.isColorBlind=s),value:!1,name:"colorBlind",class:"radio",required:""},null,512),[[T,t.value.isColorBlind]]),a[19]||(a[19]=e("span",{class:"radio-text"},"否",-1))]),e("label",He,[x(e("input",{type:"radio","onUpdate:modelValue":a[7]||(a[7]=s=>t.value.isColorBlind=s),value:!0,name:"colorBlind",class:"radio",required:""},null,512),[[T,t.value.isColorBlind]]),a[20]||(a[20]=e("span",{class:"radio-text"},"是",-1))])])])]),a[22]||(a[22]=e("div",{class:"form-group"},null,-1)),a[23]||(a[23]=e("div",{class:"form-group"},null,-1))])]),e("div",Oe,[e("label",Re,[x(e("input",{type:"checkbox","onUpdate:modelValue":a[8]||(a[8]=s=>w.value=s),required:"",class:"checkbox"},null,512),[[te,w.value]]),e("span",{class:"checkbox-text"},[a[24]||(a[24]=O(" 我已阅读并同意 ",-1)),e("button",{type:"button",class:"link-button",onClick:m}," 《用户协议》 "),a[25]||(a[25]=O(" 和 ",-1)),e("button",{type:"button",class:"link-button",onClick:U}," 《隐私政策》 ")])])]),e("button",{type:"submit",disabled:r.value||!l.value,class:"btn btn-primary register-button","aria-busy":r.value},[r.value?(n(),v("span",Ne)):b("",!0),O(" "+q(r.value?"注册中...":"立即注册"),1)],8,Le)],32),p.value?(n(),v("div",Te,[e("div",ze,[a[27]||(a[27]=e("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor",class:"success-icon"},[e("path",{d:"M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"})],-1)),e("div",null,[a[26]||(a[26]=e("div",{class:"success-title"},"注册成功！",-1)),e("div",De,"您的患者编号："+q(C.value),1)])])])):b("",!0),i.value?(n(),v("div",Ee,q(i.value),1)):b("",!0)])]),g.value?(n(),N(ue,{key:0,onClose:a[9]||(a[9]=s=>g.value=!1),onAgree:S})):b("",!0),f.value?(n(),N(me,{key:1,onClose:a[10]||(a[10]=s=>f.value=!1),onAgree:E})):b("",!0)])}}}),Xe=I(Fe,[["__scopeId","data-v-8289aa97"]]);export{Xe as default};
