<template>
  <div class="patient-management">
    <div class="patient-layout">
      <!-- 左侧：正在诊断的患者列表 -->
      <div class="patient-list-section">
        <div class="section-header">
          <h3>正在诊断的患者</h3>
          <el-button type="primary" @click="showAddPatientDialog = true">
            <el-icon><Plus /></el-icon>
            快速添加患者
          </el-button>
        </div>
        
        <div class="patient-search">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索患者姓名或编号"
            :prefix-icon="Search"
            clearable
            @input="handleSearch"
          />
        </div>
        
        <div class="patient-list">
          <div
            v-for="patient in filteredPatients"
            :key="patient.id"
            class="patient-item"
            :class="{ active: selectedPatient?.id === patient.id }"
            @click="selectPatient(patient)"
          >
            <div class="patient-avatar">
              <el-avatar :size="40">
                {{ patient.name.charAt(0) }}
              </el-avatar>
            </div>
            <div class="patient-info">
              <div class="patient-name">{{ patient.name }}</div>
              <div class="patient-details">
                <span class="patient-id">{{ patient.patientId }}</span>
                <span class="patient-age">{{ patient.age }}岁</span>
                <span class="patient-gender">{{ patient.gender }}</span>
              </div>
              <div class="patient-status">
                <el-tag :type="getStatusType(patient.status)" size="small">
                  {{ patient.status }}
                </el-tag>
                <span class="last-visit">{{ formatDate(patient.lastVisit) }}</span>
              </div>
            </div>
            <div class="patient-actions">
              <el-dropdown @command="handlePatientAction">
                <el-button text>
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{ action: 'edit', patient }">编辑</el-dropdown-item>
                    <el-dropdown-item :command="{ action: 'tests', patient }">查看测试</el-dropdown-item>
                    <el-dropdown-item :command="{ action: 'reports', patient }">生成报告</el-dropdown-item>
                    <el-dropdown-item divided :command="{ action: 'delete', patient }">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
        
        <div v-if="filteredPatients.length === 0" class="empty-state">
          <el-empty description="暂无患者数据">
            <el-button type="primary" @click="showAddPatientDialog = true">
              添加第一个患者
            </el-button>
          </el-empty>
        </div>
      </div>
      
      <!-- 右侧：患者详细信息 -->
      <div class="patient-detail-section">
        <div v-if="selectedPatient" class="patient-detail">
          <div class="detail-header">
            <div class="patient-basic-info">
              <el-avatar :size="60">
                {{ selectedPatient.name.charAt(0) }}
              </el-avatar>
              <div class="basic-info-text">
                <h2>{{ selectedPatient.name }}</h2>
                <div class="info-tags">
                  <el-tag>{{ selectedPatient.patientId }}</el-tag>
                  <el-tag type="info">{{ selectedPatient.age }}岁</el-tag>
                  <el-tag type="info">{{ selectedPatient.gender }}</el-tag>
                </div>
              </div>
            </div>
            <div class="detail-actions">
              <el-button @click="editPatient(selectedPatient)">
                <el-icon><Edit /></el-icon>
                编辑信息
              </el-button>
              <el-button type="primary">
                <el-icon><Plus /></el-icon>
                分配测试
              </el-button>
            </div>
          </div>
          
          <el-tabs v-model="activeTab" class="patient-tabs">
            <el-tab-pane label="基本信息" name="info">
              <div class="info-grid">
                <div class="info-item">
                  <label>就诊卡号</label>
                  <span>{{ selectedPatient.patientId }}</span>
                </div>
                <div class="info-item">
                  <label>姓名</label>
                  <span>{{ selectedPatient.name }}</span>
                </div>
                <div class="info-item">
                  <label>联系电话</label>
                  <span>{{ selectedPatient.phone }}</span>
                </div>
                <div class="info-item">
                  <label>身份证号</label>
                  <span>{{ selectedPatient.idCard }}</span>
                </div>
                <div class="info-item">
                  <label>性别</label>
                  <span>{{ selectedPatient.gender }}</span>
                </div>
                <div class="info-item">
                  <label>出生日期</label>
                  <span>{{ selectedPatient.birthDate }}</span>
                </div>
                <div class="info-item">
                  <label>年龄</label>
                  <span>{{ selectedPatient.age }}岁</span>
                </div>
                <div class="info-item">
                  <label>诊断状态</label>
                  <span>{{ selectedPatient.diagnosisStatus }}</span>
                </div>
                <div class="info-item">
                  <label>既往病史</label>
                  <span>{{ selectedPatient.medicalHistory }}</span>
                </div>
                <div class="info-item">
                  <label>过敏史</label>
                  <span>{{ selectedPatient.allergyHistory }}</span>
                </div>
                <div class="info-item">
                  <label>护理等级</label>
                  <span>{{ selectedPatient.careLevel }}</span>
                </div>
                <div class="info-item full-width">
                  <label>诊断信息</label>
                  <span>{{ selectedPatient.diagnosisInfo }}</span>
                </div>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="测试历史" name="tests">
              <div class="test-history">
                <div class="test-summary">
                  <div class="summary-card">
                    <div class="summary-number">{{ selectedPatient.testCount || 0 }}</div>
                    <div class="summary-label">总测试次数</div>
                  </div>
                  <div class="summary-card">
                    <div class="summary-number">{{ selectedPatient.completedTests || 0 }}</div>
                    <div class="summary-label">已完成测试</div>
                  </div>
                  <div class="summary-card">
                    <div class="summary-number">{{ selectedPatient.pendingTests || 0 }}</div>
                    <div class="summary-label">待评分测试</div>
                  </div>
                </div>
                
                <el-table :data="testHistory" class="test-table">
                  <el-table-column prop="taskName" label="任务名称" width="150" />
                  <el-table-column label="测试项目" width="200">
                    <template #default="{ row }">
                      <span>{{ row.testItems.join(', ') }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="department" label="科室" width="100" />
                  <el-table-column prop="doctor" label="主治医师" width="100" />
                  <el-table-column prop="testDate" label="测试日期" width="120" />
                  <el-table-column prop="status" label="状态" width="100">
                    <template #default="{ row }">
                      <el-tag :type="getTestStatusType(row.status)">
                        {{ row.status }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="180">
                    <template #default="{ row }">
                      <el-button text type="primary" @click="viewTestDetail(row)">
                        查看详情
                      </el-button>
                      <el-button v-if="row.status === '待评分'" text type="warning" @click="scoreTest(row)">
                        评分
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="报告中心" name="reports">
              <div class="reports-section">
                <div class="reports-header">
                  <h4>生成的报告</h4>
                  <el-button type="primary" @click="generateReport">
                    <el-icon><Document /></el-icon>
                    生成新报告
                  </el-button>
                </div>
                
                <el-table :data="reports" class="reports-table">
                  <el-table-column prop="reportName" label="报告名称" />
                  <el-table-column prop="generateDate" label="生成日期" />
                  <el-table-column prop="reportType" label="报告类型" />
                  <el-table-column label="操作">
                    <template #default="{ row }">
                      <el-button text type="primary" @click="previewReport(row)">
                        预览
                      </el-button>
                      <el-button text @click="downloadReport(row)">
                        下载
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
        
        <div v-else class="no-patient-selected">
          <el-empty description="请从左侧选择一个患者查看详细信息" />
        </div>
      </div>
    </div>
    
    <!-- 添加患者对话框 -->
    <el-dialog
      v-model="showAddPatientDialog"
      title="添加新患者"
      width="600px"
      @close="resetAddPatientForm"
    >
      <el-form
        ref="addPatientFormRef"
        :model="addPatientForm"
        :rules="addPatientRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="addPatientForm.name" placeholder="请输入患者姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-select v-model="addPatientForm.gender" placeholder="请选择性别">
                <el-option label="男" value="男" />
                <el-option label="女" value="女" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="出生日期" prop="birthDate">
              <el-date-picker
                v-model="addPatientForm.birthDate"
                type="date"
                placeholder="选择出生日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="addPatientForm.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="紧急联系人" prop="emergencyContact">
              <el-input v-model="addPatientForm.emergencyContact" placeholder="请输入紧急联系人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="紧急联系电话" prop="emergencyPhone">
              <el-input v-model="addPatientForm.emergencyPhone" placeholder="请输入紧急联系电话" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="备注">
          <el-input
            v-model="addPatientForm.notes"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（可选）"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showAddPatientDialog = false">取消</el-button>
        <el-button type="primary" @click="addPatient" :loading="addingPatient">
          确定添加
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Search,
  MoreFilled,
  Edit,
  Document
} from '@element-plus/icons-vue'

// 接口定义
interface Patient {
  id: string
  patientId: string
  name: string
  phone: string
  idCard: string
  gender: string
  birthDate: string
  age: number
  diagnosisStatus: string
  medicalHistory: string
  allergyHistory: string
  careLevel: string
  diagnosisInfo: string
  emergencyContact: string
  emergencyPhone: string
  status: string
  lastVisit: string
  notes?: string
  testCount?: number
  completedTests?: number
  pendingTests?: number
}

interface TestRecord {
  id: string
  taskName: string
  testName: string
  testItems: string[]
  department: string
  doctor: string
  testDate: string
  status: string
  score?: string
}

interface Report {
  id: string
  reportName: string
  generateDate: string
  reportType: string
}

// 响应式数据
const searchKeyword = ref('')
const selectedPatient = ref<Patient | null>(null)
const activeTab = ref('info')
const showAddPatientDialog = ref(false)
const addingPatient = ref(false)

// 患者列表数据
const patients = ref<Patient[]>([
  {
    id: '1',
    patientId: 'P001',
    name: '张三',
    phone: '13800138001',
    idCard: '110101195803151234',
    gender: '男',
    birthDate: '1958-03-15',
    age: 65,
    diagnosisStatus: '正在诊断',
    medicalHistory: '高血压病史10年，糖尿病病史5年',
    allergyHistory: '青霉素过敏',
    careLevel: '一级护理',
    diagnosisInfo: '轻度认知障碍，疑似阿尔茨海默病早期',
    emergencyContact: '李四',
    emergencyPhone: '13800138002',
    status: '正在诊断',
    lastVisit: '2024-01-15',
    notes: '轻度认知障碍，需要定期复查',
    testCount: 5,
    completedTests: 3,
    pendingTests: 2
  },
  {
    id: '2',
    patientId: 'P002',
    name: '王芳',
    phone: '13800138003',
    idCard: '110101195108225678',
    gender: '女',
    birthDate: '1951-08-22',
    age: 72,
    diagnosisStatus: '待复查',
    medicalHistory: '冠心病病史8年，骨质疏松',
    allergyHistory: '无已知过敏史',
    careLevel: '二级护理',
    diagnosisInfo: '中度认知功能下降，血管性痴呆可能',
    emergencyContact: '王明',
    emergencyPhone: '13800138004',
    status: '待复查',
    lastVisit: '2024-01-10',
    notes: '记忆力下降明显',
    testCount: 3,
    completedTests: 3,
    pendingTests: 0
  },
  {
    id: '3',
    patientId: 'P003',
    name: '李明',
    phone: '13800138005',
    idCard: '110101196512059012',
    gender: '男',
    birthDate: '1965-12-05',
    age: 58,
    diagnosisStatus: '正在诊断',
    medicalHistory: '无重大疾病史',
    allergyHistory: '海鲜过敏',
    careLevel: '三级护理',
    diagnosisInfo: '主观认知下降，需进一步评估',
    emergencyContact: '李红',
    emergencyPhone: '13800138006',
    status: '正在诊断',
    lastVisit: '2024-01-20',
    notes: '',
    testCount: 2,
    completedTests: 1,
    pendingTests: 1
  }
])

// 测试历史数据
const testHistory = ref<TestRecord[]>([
  {
    id: '1',
    taskName: '认知评估任务A',
    testName: '词语流畅性测试',
    testItems: ['语义流畅性', '语音流畅性', '转换流畅性'],
    department: '神经内科',
    doctor: '张医生',
    testDate: '2024-01-15',
    status: '待评分',
    score: '-'
  },
  {
    id: '2',
    taskName: '记忆功能评估',
    testName: '数字广度测试',
    testItems: ['顺背数字', '倒背数字'],
    department: '神经内科',
    doctor: '李医生',
    testDate: '2024-01-15',
    status: '已完成',
    score: '85分'
  },
  {
    id: '3',
    taskName: '注意力评估',
    testName: '注意力测试',
    testItems: ['持续注意', '选择注意', '分配注意'],
    department: '心理科',
    doctor: '王医生',
    testDate: '2024-01-10',
    status: '已完成',
    score: '78分'
  }
])

// 报告数据
const reports = ref<Report[]>([
  {
    id: '1',
    reportName: '认知能力评估报告',
    generateDate: '2024-01-16',
    reportType: '综合报告'
  },
  {
    id: '2',
    reportName: '词语流畅性专项报告',
    generateDate: '2024-01-12',
    reportType: '专项报告'
  }
])

// 添加患者表单
const addPatientForm = ref({
  name: '',
  gender: '',
  birthDate: '',
  phone: '',
  emergencyContact: '',
  emergencyPhone: '',
  notes: ''
})

// 表单验证规则
const addPatientRules = {
  name: [
    { required: true, message: '请输入患者姓名', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ],
  birthDate: [
    { required: true, message: '请选择出生日期', trigger: 'change' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  emergencyContact: [
    { required: true, message: '请输入紧急联系人', trigger: 'blur' }
  ],
  emergencyPhone: [
    { required: true, message: '请输入紧急联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
}

const addPatientFormRef = ref()

// 计算属性
const filteredPatients = computed(() => {
  if (!searchKeyword.value) {
    return patients.value
  }
  return patients.value.filter(patient =>
    patient.name.includes(searchKeyword.value) ||
    patient.patientId.includes(searchKeyword.value)
  )
})

// 方法
const selectPatient = (patient: Patient) => {
  selectedPatient.value = patient
  activeTab.value = 'info'
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
}

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '正在诊断': 'warning',
    '待复查': 'info',
    '已完成': 'success',
    '暂停': 'danger'
  }
  return statusMap[status] || 'info'
}

const getTestStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    '已完成': 'success',
    '待评分': 'warning',
    '进行中': 'info',
    '已取消': 'danger'
  }
  return statusMap[status] || 'info'
}

const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  return date.toLocaleDateString('zh-CN')
}

const calculateAge = (birthDate: string) => {
  const birth = new Date(birthDate)
  const today = new Date()
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }

  return age
}

const handlePatientAction = ({ action, patient }: { action: string, patient: Patient }) => {
  switch (action) {
    case 'edit':
      editPatient(patient)
      break
    case 'tests':
      selectPatient(patient)
      activeTab.value = 'tests'
      break
    case 'reports':
      selectPatient(patient)
      activeTab.value = 'reports'
      break
    case 'delete':
      deletePatient(patient)
      break
  }
}

const editPatient = (patient: Patient) => {
  ElMessage.info('编辑患者功能开发中...')
}

const deletePatient = async (patient: Patient) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除患者 ${patient.name} 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const index = patients.value.findIndex(p => p.id === patient.id)
    if (index > -1) {
      patients.value.splice(index, 1)
      if (selectedPatient.value?.id === patient.id) {
        selectedPatient.value = null
      }
      ElMessage.success('患者删除成功')
    }
  } catch {
    // 用户取消删除
  }
}

const addPatient = async () => {
  if (!addPatientFormRef.value) return

  try {
    await addPatientFormRef.value.validate()
    addingPatient.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    const newPatient: Patient = {
      id: Date.now().toString(),
      patientId: `P${String(patients.value.length + 1).padStart(3, '0')}`,
      name: addPatientForm.value.name,
      gender: addPatientForm.value.gender,
      age: calculateAge(addPatientForm.value.birthDate),
      birthDate: addPatientForm.value.birthDate,
      phone: addPatientForm.value.phone,
      emergencyContact: addPatientForm.value.emergencyContact,
      emergencyPhone: addPatientForm.value.emergencyPhone,
      status: '正在诊断',
      lastVisit: new Date().toISOString().split('T')[0],
      notes: addPatientForm.value.notes,
      testCount: 0,
      completedTests: 0,
      pendingTests: 0
    }

    patients.value.unshift(newPatient)
    showAddPatientDialog.value = false
    selectedPatient.value = newPatient

    ElMessage.success('患者添加成功')
  } catch (error) {
    console.error('添加患者失败:', error)
  } finally {
    addingPatient.value = false
  }
}

const resetAddPatientForm = () => {
  addPatientForm.value = {
    name: '',
    gender: '',
    birthDate: '',
    phone: '',
    emergencyContact: '',
    emergencyPhone: '',
    notes: ''
  }
  addPatientFormRef.value?.resetFields()
}

const viewTestDetail = (test: TestRecord) => {
  ElMessage.info('查看测试详情功能开发中...')
}

const scoreTest = (test: TestRecord) => {
  ElMessage.info('测试评分功能开发中...')
}

const generateReport = () => {
  ElMessage.info('生成报告功能开发中...')
}

const previewReport = (report: Report) => {
  ElMessage.info('预览报告功能开发中...')
}

const downloadReport = (report: Report) => {
  ElMessage.info('下载报告功能开发中...')
}

// 生命周期
onMounted(() => {
  // 默认选择第一个患者
  if (patients.value.length > 0) {
    selectedPatient.value = patients.value[0]
  }
})
</script>

<style scoped>
.patient-management {
  height: 100%;
  width: 100%;
}

.patient-layout {
  display: flex;
  height: 100%;
  gap: 24px;
}

/* 左侧患者列表 */
.patient-list-section {
  flex: 2;
  background: white;
  border-radius: 8px;
  padding: 24px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.patient-search {
  margin-bottom: 16px;
}

.patient-list {
  flex: 1;
  overflow-y: auto;
}

.patient-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.patient-item:hover {
  background-color: #f9fafb;
  border-color: #e5e7eb;
}

.patient-item.active {
  background-color: #eff6ff;
  border-color: #3b82f6;
}

.patient-avatar {
  margin-right: 12px;
}

.patient-info {
  flex: 1;
  min-width: 0;
}

.patient-name {
  font-weight: 600;
  font-size: 16px;
  color: #1f2937;
  margin-bottom: 4px;
}

.patient-details {
  display: flex;
  gap: 8px;
  margin-bottom: 4px;
}

.patient-id,
.patient-age,
.patient-gender {
  font-size: 12px;
  color: #6b7280;
}

.patient-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.last-visit {
  font-size: 12px;
  color: #9ca3af;
}

.patient-actions {
  margin-left: 8px;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

/* 右侧患者详情 */
.patient-detail-section {
  flex: 5;
  background: white;
  border-radius: 8px;
  padding: 24px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.patient-detail {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.patient-basic-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.basic-info-text h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.info-tags {
  display: flex;
  gap: 8px;
}

.detail-actions {
  display: flex;
  gap: 12px;
}

.patient-tabs {
  flex: 1;
  overflow: hidden;
}

.patient-tabs :deep(.el-tabs__content) {
  height: calc(100% - 40px);
  overflow-y: auto;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding: 20px 0;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

.info-item label {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
}

.info-item span {
  font-size: 16px;
  color: #1f2937;
}

.test-history {
  padding: 20px 0;
}

.test-summary {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 24px;
}

.summary-card {
  background: #f9fafb;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.summary-number {
  font-size: 32px;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 4px;
}

.summary-label {
  font-size: 14px;
  color: #6b7280;
}

.test-table {
  margin-top: 16px;
}

.reports-section {
  padding: 20px 0;
}

.reports-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.reports-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.reports-table {
  margin-top: 16px;
}

.no-patient-selected {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .patient-layout {
    flex-direction: column;
    height: auto;
  }

  .patient-list-section {
    flex: none;
    height: 400px;
  }

  .patient-detail-section {
    flex: none;
    min-height: 600px;
  }
}

@media (max-width: 768px) {
  .patient-layout {
    gap: 16px;
  }

  .patient-list-section,
  .patient-detail-section {
    padding: 16px;
  }

  .detail-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .test-summary {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .test-table {
    font-size: 12px;
  }

  .test-table .el-table__cell {
    padding: 8px 4px;
  }
}

/* 平板设备 */
@media (max-width: 1024px) and (min-width: 769px) {
  .info-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
}
</style>
