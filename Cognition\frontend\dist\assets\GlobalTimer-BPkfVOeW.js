import{c as p,r as k,d as x,b as m,f as g,B as o,e as n,t as f,l as d,_ as S}from"./index-tHlaP7TV.js";const e=k({isRunning:!1,startTime:null,elapsedTime:0,currentTask:null});let r=null;class i{static start(t="认知测试任务"){e.value.isRunning||(e.value.startTime=Date.now(),e.value.isRunning=!0,e.value.currentTask=t,e.value.elapsedTime=0,r=window.setInterval(()=>{e.value.startTime&&(e.value.elapsedTime=Math.floor((Date.now()-e.value.startTime)/1e3))},1e3),console.log(`全局计时器已启动: ${t}`))}static pause(){e.value.isRunning&&r&&(clearInterval(r),r=null,e.value.isRunning=!1,console.log("全局计时器已暂停"))}static resume(){!e.value.isRunning&&e.value.startTime&&(e.value.startTime=Date.now()-e.value.elapsedTime*1e3,e.value.isRunning=!0,r=window.setInterval(()=>{e.value.startTime&&(e.value.elapsedTime=Math.floor((Date.now()-e.value.startTime)/1e3))},1e3),console.log("全局计时器已恢复"))}static stop(){r&&(clearInterval(r),r=null);const t=e.value.elapsedTime,l=e.value.currentTask;return e.value.isRunning=!1,e.value.startTime=null,e.value.elapsedTime=0,e.value.currentTask=null,console.log(`全局计时器已停止: ${l}, 总用时: ${t}秒`),t}static reset(){this.stop(),console.log("全局计时器已重置")}static getState(){return e.value}static getFormattedTime(){const t=e.value.elapsedTime,l=Math.floor(t/3600),a=Math.floor(t%3600/60),u=t%60;return l>0?`${l.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}`:`${a.toString().padStart(2,"0")}:${u.toString().padStart(2,"0")}`}static isRunning(){return e.value.isRunning}static getCurrentTask(){return e.value.currentTask}static getElapsedTime(){return e.value.elapsedTime}}function C(){const v=p(()=>e.value.isRunning),t=p(()=>e.value.elapsedTime),l=p(()=>e.value.currentTask),a=p(()=>i.getFormattedTime());return{isRunning:v,elapsedTime:t,currentTask:l,formattedTime:a,start:i.start,pause:i.pause,resume:i.resume,stop:i.stop,reset:i.reset}}const b={key:0,class:"global-timer"},y={class:"timer-content"},R={class:"timer-text"},B={key:0,class:"task-name"},_={key:0,class:"timer-controls"},$=x({__name:"GlobalTimer",props:{showControls:{type:Boolean,default:!1},position:{default:"top-right"},size:{default:"medium"}},setup(v){const{isRunning:t,currentTask:l,formattedTime:a,pause:u,resume:w,stop:T}=C();return(h,s)=>o(t)?(d(),m("div",b,[n("div",y,[s[3]||(s[3]=n("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"2"}),n("polyline",{points:"12,6 12,12 16,14",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)),n("span",R,f(o(a)),1),o(l)?(d(),m("span",B,f(o(l)),1)):g("",!0)]),h.showControls?(d(),m("div",_,[o(t)?(d(),m("button",{key:0,onClick:s[0]||(s[0]=(...c)=>o(u)&&o(u)(...c)),class:"control-btn"},s[4]||(s[4]=[n("svg",{width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("rect",{x:"6",y:"4",width:"4",height:"16",fill:"currentColor"}),n("rect",{x:"14",y:"4",width:"4",height:"16",fill:"currentColor"})],-1)]))):(d(),m("button",{key:1,onClick:s[1]||(s[1]=(...c)=>o(w)&&o(w)(...c)),class:"control-btn"},s[5]||(s[5]=[n("svg",{width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("polygon",{points:"5,3 19,12 5,21",fill:"currentColor"})],-1)]))),n("button",{onClick:s[2]||(s[2]=(...c)=>o(T)&&o(T)(...c)),class:"control-btn stop-btn"},s[6]||(s[6]=[n("svg",{width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("rect",{x:"3",y:"3",width:"18",height:"18",fill:"currentColor"})],-1)]))])):g("",!0)])):g("",!0)}}),D=S($,[["__scopeId","data-v-828d04e7"]]);export{i as G,D as a,C as u};
