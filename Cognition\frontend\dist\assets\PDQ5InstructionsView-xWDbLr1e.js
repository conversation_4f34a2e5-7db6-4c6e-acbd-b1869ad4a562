import{d as x,r as u,o as y,m as C,b as e,e as t,h as _,t as i,B as p,a6 as S,f as L,n as V,F as B,x as M,g as T,a as j,l as o,_ as q}from"./index-tHlaP7TV.js";import{S as D,s as r,p as H,a as A}from"./speech-BAaOvQQ9.js";import{G as v,a as I}from"./GlobalTimer-BPkfVOeW.js";const N={class:"instructions-container test-container"},G={class:"header-section"},P={class:"page-title"},Q={class:"header-right"},z={class:"instructions-card"},F={class:"instructions-content"},R={class:"content-header"},Z=["disabled"],E={key:0,width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},U={key:1,width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},J={class:"sections-cards"},K={class:"card-header"},O={class:"section-number"},W={class:"section-title"},X={class:"card-content"},Y={class:"section-description"},$={class:"section-stats"},tt={class:"question-count"},st={class:"action-section"},et=["disabled"],ot={key:0},nt={key:1,class:"loading-text"},it=x({__name:"PDQ5InstructionsView",setup(at){const h=j(),c=u(!1),n=u(!1),k=u(D.isSupported()),a=H,g=A.sections,m=()=>{r.stop(),h.back()},w=async()=>{if(n.value)r.stop(),n.value=!1;else try{n.value=!0;const l=Array.isArray(a.content)?a.content.join(" "):a.content;await r.speak(l),n.value=!1}catch(l){console.error("语音播报失败:",l),n.value=!1}},f=()=>{r.stop(),c.value=!0,setTimeout(()=>{h.push({path:"/tests/pdq5"})},500)};return y(()=>{v.isRunning()||v.start("PDQ-5测试")}),C(()=>{r.stop()}),(l,s)=>(o(),e("div",N,[t("div",G,[t("button",{onClick:m,class:"back-button","aria-label":"返回"},s[0]||(s[0]=[t("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[t("path",{d:"M19 12H5M12 19L5 12L12 5",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)])),_(I,{position:"title-left",size:"medium"}),t("h1",P,i(p(a).title),1),t("div",Q,[_(S)])]),t("div",z,[t("div",F,[t("div",R,[s[3]||(s[3]=t("h2",{class:"content-title"},"测试单元",-1)),k.value?(o(),e("button",{key:0,onClick:w,class:V(["speech-button",{active:n.value}]),disabled:c.value,"aria-label":"语音播报"},[n.value?(o(),e("svg",U,s[2]||(s[2]=[t("path",{d:"M11 5L6 9H2V15H6L11 19V5Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),t("path",{d:"M23 9L17 15M17 9L23 15",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))):(o(),e("svg",E,s[1]||(s[1]=[t("path",{d:"M11 5L6 9H2V15H6L11 19V5Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),t("path",{d:"M19.07 4.93A10 10 0 0 1 19.07 19.07M15.54 8.46A5 5 0 0 1 15.54 15.54",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)])))],10,Z)):L("",!0)]),t("div",J,[(o(!0),e(B,null,M(p(g),(d,b)=>(o(),e("div",{class:"section-card",key:d.id},[t("div",K,[t("div",O,i(b+1),1),t("h3",W,i(d.name),1)]),t("div",X,[t("p",Y,i(d.description),1),t("div",$,[t("span",tt,i(d.questions.length)+"题",1)])])]))),128))])]),t("div",st,[t("button",{onClick:f,class:"start-button",disabled:c.value},[c.value?(o(),e("span",nt,s[4]||(s[4]=[t("div",{class:"loading-spinner"},null,-1),T(" 准备中... ",-1)]))):(o(),e("span",ot,i(p(a).startButtonText),1))],8,et)])])]))}}),dt=q(it,[["__scopeId","data-v-09aa2719"]]);export{dt as default};
