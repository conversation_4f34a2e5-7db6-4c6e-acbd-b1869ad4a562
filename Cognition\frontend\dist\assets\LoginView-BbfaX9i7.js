import{d as M,u as z,r as u,c as V,o as w,a as k,b as v,e as t,f as h,w as x,v as L,g as p,t as g,h as C,i as N,j as T,k as B,l as d,_ as H}from"./index-tHlaP7TV.js";const I={class:"login-container"},S={class:"input-group"},A=["type","aria-label"],D=["disabled","aria-busy"],q={key:0,class:"loading-spinner","aria-hidden":"true"},P={class:"register-link"},R={key:0,role:"alert",class:"error-message","aria-live":"polite"},j=M({__name:"LoginView",setup(E){const c=k(),l=z(),a=u(!1),o=u(""),n=u({value:""}),m=V(()=>"请输入患者编号或手机号进行登录"),_=s=>/^1[3-9]\d{9}$/.test(s)?"phone":(/^P\d+/.test(s),"patientNumber"),b=()=>{const s=n.value.value;return/^1[3-9]/.test(s)?"tel":"text"},f=async()=>{const s=n.value.value.trim();if(!s){o.value="请输入患者编号或手机号";return}a.value=!0,o.value="";try{const i={type:_(s),value:s},r=await l.login(i);r.success?await c.push("/home"):o.value=r.message}catch(e){o.value="登录失败，请稍后重试",console.error("Login error:",e)}finally{a.value=!1}},y=()=>{alert(`二维码登录功能开发中...

您可以使用以下测试账户：
• 患者编号：P123456001
• 手机号：13800138000`)};return w(()=>{l.restoreAuth(),l.isLoggedIn&&c.push("/home")}),(s,e)=>{const i=T("router-link");return d(),v("div",I,[e[4]||(e[4]=t("div",{class:"main-title"},[t("h1",null,"认知测试系统")],-1)),t("form",{onSubmit:B(f,["prevent"]),class:"login-form"},[t("div",S,[x(t("input",{"onUpdate:modelValue":e[0]||(e[0]=r=>n.value.value=r),type:b(),placeholder:"请输入患者编号或手机号",class:"login-input",required:"","aria-label":m.value},null,8,A),[[L,n.value.value]]),t("button",{type:"submit",disabled:a.value||!n.value.value,class:"login-button","aria-busy":a.value},[a.value?(d(),v("span",q)):h("",!0),p(" "+g(a.value?"登录中...":"登录"),1)],8,D),t("button",{type:"button",onClick:y,class:"qr-button","aria-label":"二维码登录",title:"二维码登录"},e[1]||(e[1]=[t("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor"},[t("path",{d:"M3 11h8V3H3v8zm2-6h4v4H5V5zM3 21h8v-8H3v8zm2-6h4v4H5v-4zM13 3v8h8V3h-8zm6 6h-4V5h4v4zM19 13h2v2h-2zM13 13h2v2h-2zM15 15h2v2h-2zM13 17h2v2h-2zM15 19h2v2h-2zM17 17h2v2h-2zM17 19h2v2h-2zM19 15h2v2h-2zM19 17h2v2h-2z"})],-1)]))]),t("div",P,[e[3]||(e[3]=t("span",{class:"register-text"},"没有患者代码？",-1)),C(i,{to:"/register",class:"register-btn"},{default:N(()=>e[2]||(e[2]=[p(" 立即注册 ",-1)])),_:1,__:[2]})])],32),o.value?(d(),v("div",R,g(o.value),1)):h("",!0)])}}}),Q=H(j,[["__scopeId","data-v-a8dbb451"]]);export{Q as default};
