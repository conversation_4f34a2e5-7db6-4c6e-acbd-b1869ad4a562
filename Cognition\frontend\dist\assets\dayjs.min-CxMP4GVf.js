var Q=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function K(g){return g&&g.__esModule&&Object.prototype.hasOwnProperty.call(g,"default")?g.default:g}var V={exports:{}};(function(g,X){(function(A,T){g.exports=T()})(Q,function(){var A=1e3,T=6e4,U=36e5,I="millisecond",S="second",b="minute",O="hour",y="day",x="week",m="month",J="quarter",M="year",_="date",Z="Invalid Date",P=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,q=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,B={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(s){var n=["th","st","nd","rd"],t=s%100;return"["+s+(n[(t-20)%10]||n[t]||n[0])+"]"}},F=function(s,n,t){var r=String(s);return!r||r.length>=n?s:""+Array(n+1-r.length).join(t)+s},G={s:F,z:function(s){var n=-s.utcOffset(),t=Math.abs(n),r=Math.floor(t/60),e=t%60;return(n<=0?"+":"-")+F(r,2,"0")+":"+F(e,2,"0")},m:function s(n,t){if(n.date()<t.date())return-s(t,n);var r=12*(t.year()-n.year())+(t.month()-n.month()),e=n.clone().add(r,m),i=t-e<0,u=n.clone().add(r+(i?-1:1),m);return+(-(r+(t-e)/(i?e-u:u-e))||0)},a:function(s){return s<0?Math.ceil(s)||0:Math.floor(s)},p:function(s){return{M:m,y:M,w:x,d:y,D:_,h:O,m:b,s:S,ms:I,Q:J}[s]||String(s||"").toLowerCase().replace(/s$/,"")},u:function(s){return s===void 0}},Y="en",p={};p[Y]=B;var z="$isDayjsObject",N=function(s){return s instanceof L||!(!s||!s[z])},C=function s(n,t,r){var e;if(!n)return Y;if(typeof n=="string"){var i=n.toLowerCase();p[i]&&(e=i),t&&(p[i]=t,e=i);var u=n.split("-");if(!e&&u.length>1)return s(u[0])}else{var o=n.name;p[o]=n,e=o}return!r&&e&&(Y=e),e||!r&&Y},f=function(s,n){if(N(s))return s.clone();var t=typeof n=="object"?n:{};return t.date=s,t.args=arguments,new L(t)},a=G;a.l=C,a.i=N,a.w=function(s,n){return f(s,{locale:n.$L,utc:n.$u,x:n.$x,$offset:n.$offset})};var L=function(){function s(t){this.$L=C(t.locale,null,!0),this.parse(t),this.$x=this.$x||t.x||{},this[z]=!0}var n=s.prototype;return n.parse=function(t){this.$d=function(r){var e=r.date,i=r.utc;if(e===null)return new Date(NaN);if(a.u(e))return new Date;if(e instanceof Date)return new Date(e);if(typeof e=="string"&&!/Z$/i.test(e)){var u=e.match(P);if(u){var o=u[2]-1||0,c=(u[7]||"0").substring(0,3);return i?new Date(Date.UTC(u[1],o,u[3]||1,u[4]||0,u[5]||0,u[6]||0,c)):new Date(u[1],o,u[3]||1,u[4]||0,u[5]||0,u[6]||0,c)}}return new Date(e)}(t),this.init()},n.init=function(){var t=this.$d;this.$y=t.getFullYear(),this.$M=t.getMonth(),this.$D=t.getDate(),this.$W=t.getDay(),this.$H=t.getHours(),this.$m=t.getMinutes(),this.$s=t.getSeconds(),this.$ms=t.getMilliseconds()},n.$utils=function(){return a},n.isValid=function(){return this.$d.toString()!==Z},n.isSame=function(t,r){var e=f(t);return this.startOf(r)<=e&&e<=this.endOf(r)},n.isAfter=function(t,r){return f(t)<this.startOf(r)},n.isBefore=function(t,r){return this.endOf(r)<f(t)},n.$g=function(t,r,e){return a.u(t)?this[r]:this.set(e,t)},n.unix=function(){return Math.floor(this.valueOf()/1e3)},n.valueOf=function(){return this.$d.getTime()},n.startOf=function(t,r){var e=this,i=!!a.u(r)||r,u=a.p(t),o=function(w,l){var v=a.w(e.$u?Date.UTC(e.$y,l,w):new Date(e.$y,l,w),e);return i?v:v.endOf(y)},c=function(w,l){return a.w(e.toDate()[w].apply(e.toDate("s"),(i?[0,0,0,0]:[23,59,59,999]).slice(l)),e)},h=this.$W,d=this.$M,$=this.$D,k="set"+(this.$u?"UTC":"");switch(u){case M:return i?o(1,0):o(31,11);case m:return i?o(1,d):o(0,d+1);case x:var D=this.$locale().weekStart||0,H=(h<D?h+7:h)-D;return o(i?$-H:$+(6-H),d);case y:case _:return c(k+"Hours",0);case O:return c(k+"Minutes",1);case b:return c(k+"Seconds",2);case S:return c(k+"Milliseconds",3);default:return this.clone()}},n.endOf=function(t){return this.startOf(t,!1)},n.$set=function(t,r){var e,i=a.p(t),u="set"+(this.$u?"UTC":""),o=(e={},e[y]=u+"Date",e[_]=u+"Date",e[m]=u+"Month",e[M]=u+"FullYear",e[O]=u+"Hours",e[b]=u+"Minutes",e[S]=u+"Seconds",e[I]=u+"Milliseconds",e)[i],c=i===y?this.$D+(r-this.$W):r;if(i===m||i===M){var h=this.clone().set(_,1);h.$d[o](c),h.init(),this.$d=h.set(_,Math.min(this.$D,h.daysInMonth())).$d}else o&&this.$d[o](c);return this.init(),this},n.set=function(t,r){return this.clone().$set(t,r)},n.get=function(t){return this[a.p(t)]()},n.add=function(t,r){var e,i=this;t=Number(t);var u=a.p(r),o=function(d){var $=f(i);return a.w($.date($.date()+Math.round(d*t)),i)};if(u===m)return this.set(m,this.$M+t);if(u===M)return this.set(M,this.$y+t);if(u===y)return o(1);if(u===x)return o(7);var c=(e={},e[b]=T,e[O]=U,e[S]=A,e)[u]||1,h=this.$d.getTime()+t*c;return a.w(h,this)},n.subtract=function(t,r){return this.add(-1*t,r)},n.format=function(t){var r=this,e=this.$locale();if(!this.isValid())return e.invalidDate||Z;var i=t||"YYYY-MM-DDTHH:mm:ssZ",u=a.z(this),o=this.$H,c=this.$m,h=this.$M,d=e.weekdays,$=e.months,k=e.meridiem,D=function(l,v,j,W){return l&&(l[v]||l(r,i))||j[v].slice(0,W)},H=function(l){return a.s(o%12||12,l,"0")},w=k||function(l,v,j){var W=l<12?"AM":"PM";return j?W.toLowerCase():W};return i.replace(q,function(l,v){return v||function(j){switch(j){case"YY":return String(r.$y).slice(-2);case"YYYY":return a.s(r.$y,4,"0");case"M":return h+1;case"MM":return a.s(h+1,2,"0");case"MMM":return D(e.monthsShort,h,$,3);case"MMMM":return D($,h);case"D":return r.$D;case"DD":return a.s(r.$D,2,"0");case"d":return String(r.$W);case"dd":return D(e.weekdaysMin,r.$W,d,2);case"ddd":return D(e.weekdaysShort,r.$W,d,3);case"dddd":return d[r.$W];case"H":return String(o);case"HH":return a.s(o,2,"0");case"h":return H(1);case"hh":return H(2);case"a":return w(o,c,!0);case"A":return w(o,c,!1);case"m":return String(c);case"mm":return a.s(c,2,"0");case"s":return String(r.$s);case"ss":return a.s(r.$s,2,"0");case"SSS":return a.s(r.$ms,3,"0");case"Z":return u}return null}(l)||u.replace(":","")})},n.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},n.diff=function(t,r,e){var i,u=this,o=a.p(r),c=f(t),h=(c.utcOffset()-this.utcOffset())*T,d=this-c,$=function(){return a.m(u,c)};switch(o){case M:i=$()/12;break;case m:i=$();break;case J:i=$()/3;break;case x:i=(d-h)/6048e5;break;case y:i=(d-h)/864e5;break;case O:i=d/U;break;case b:i=d/T;break;case S:i=d/A;break;default:i=d}return e?i:a.a(i)},n.daysInMonth=function(){return this.endOf(m).$D},n.$locale=function(){return p[this.$L]},n.locale=function(t,r){if(!t)return this.$L;var e=this.clone(),i=C(t,r,!0);return i&&(e.$L=i),e},n.clone=function(){return a.w(this.$d,this)},n.toDate=function(){return new Date(this.valueOf())},n.toJSON=function(){return this.isValid()?this.toISOString():null},n.toISOString=function(){return this.$d.toISOString()},n.toString=function(){return this.$d.toUTCString()},s}(),E=L.prototype;return f.prototype=E,[["$ms",I],["$s",S],["$m",b],["$H",O],["$W",y],["$M",m],["$y",M],["$D",_]].forEach(function(s){E[s[1]]=function(n){return this.$g(n,s[0],s[1])}}),f.extend=function(s,n){return s.$i||(s(n,L,f),s.$i=!0),f},f.locale=C,f.isDayjs=N,f.unix=function(s){return f(1e3*s)},f.en=p[Y],f.Ls=p,f.p={},f})})(V);var R=V.exports;const tt=K(R);export{tt as d};
