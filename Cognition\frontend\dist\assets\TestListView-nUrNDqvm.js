import{d as w,r as y,b as l,e as a,y as h,F as u,x as p,p as g,n,f as z,z as C,a4 as k,t as o,g as f,a5 as e,a as x,l as s,_}from"./index-tHlaP7TV.js";const M={class:"test-list-container"},B={class:"test-header"},L={class:"test-content"},H={class:"tests-section"},V={class:"tests-grid"},S=["onClick","tabindex","aria-disabled","onKeydown"],T={class:"test-header"},D={key:0,class:"disabled-badge"},$={class:"test-info"},N={class:"test-name"},P={class:"test-description"},Q={class:"test-meta"},q={class:"meta-item"},K={class:"meta-item"},F={class:"test-features"},I={class:"test-action"},E=["disabled","aria-label"],R=w({__name:"TestListView",setup(j){const c=x(),m=y([{id:"pdq5",type:"PDQ5",name:"PDQ-5 测试",description:"评估记忆力、注意力和思维反应能力的综合测试",duration:"15-20分钟",difficulty:"中等",features:["记忆评估","注意力测试","认知灵活性"],available:!0},{id:"hopkins",type:"Hopkins",name:"Hopkins 词汇学习",description:"通过图片识别和记忆测试评估学习和记忆能力",duration:"10-15分钟",difficulty:"简单",features:["即时记忆","延迟记忆","图片识别"],available:!0},{id:"nback",type:"NBack",name:"顺背/倒背测试",description:"测试工作记忆和数字序列记忆能力",duration:"8-12分钟",difficulty:"中等",features:["工作记忆","数字记忆","认知负荷"],available:!0},{id:"stroop",type:"Stroop",name:"Stroop 色词测试",description:"评估抑制控制能力和注意力选择性",duration:"5-8分钟",difficulty:"中等",features:["抑制控制","注意选择","反应速度"],available:!0},{id:"trail",type:"Trail",name:"连线测试",description:"评估视觉搜索、处理速度和执行功能",duration:"10-15分钟",difficulty:"中等",features:["执行功能","视觉搜索","处理速度"],available:!0},{id:"verbal",type:"Verbal",name:"词语流畅性测试",description:"通过语言产出评估语言功能和执行控制",duration:"3-5分钟",difficulty:"简单",features:["语言功能","执行控制","语义记忆"],available:!0},{id:"cpt",type:"CPT",name:"持续性操作测试",description:"评估持续注意力和反应抑制能力",duration:"8-10分钟",difficulty:"中等",features:["持续注意","反应抑制","警觉性"],available:!0},{id:"dsst",type:"DSST",name:"DSST 数字符号转换",description:"评估处理速度、工作记忆和视觉-运动协调",duration:"5-8分钟",difficulty:"中等",features:["处理速度","符号转换","视觉运动"],available:!0},{id:"masked",type:"Masked",name:"掩蔽情感启动任务",description:"评估潜意识情绪处理和情感认知能力",duration:"15-20分钟",difficulty:"困难",features:["情绪识别","潜意识处理","情感认知"],available:!0}]),b=d=>{const t={PDQ5:()=>e("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor"},[e("path",{d:"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"})]),Hopkins:()=>e("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor"},[e("path",{d:"M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"})]),NBack:()=>e("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor"},[e("path",{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"})]),Stroop:()=>e("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor"},[e("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})]),Trail:()=>e("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor"},[e("path",{d:"M9.01 14H2v2h7.01v3L13 15l-3.99-4v3zm5.98-1v-3L19 14l-4.01 4v-3H8v-2h6.99z"})]),Verbal:()=>e("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor"},[e("path",{d:"M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.49 6-3.31 6-6.72h-1.7z"})]),CPT:()=>e("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor"},[e("path",{d:"M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"}),e("path",{d:"M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"})]),DSST:()=>e("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor"},[e("path",{d:"M6 2c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h16c1.1 0 2-.9 2-2V8l-6-6H6zm7 7V3.5L18.5 9H13z"})]),Masked:()=>e("svg",{width:24,height:24,viewBox:"0 0 24 24",fill:"currentColor"},[e("path",{d:"M9 11H7v2h2v-2zm4 0h-2v2h2v-2zm4 0h-2v2h2v-2zm2-7h-1V2h-2v2H8V2H6v2H5c-1.1 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z"})])};return t[d]||t.PDQ5},v=d=>{if(d.available)switch(d.id){case"pdq5":c.push("/tests/pdq5/instructions");break;case"masked":c.push("/tests/masked-emotion");break;default:alert(`即将开始 ${d.name}，请确保您已阅读测试须知`);break}};return(d,t)=>(s(),l("div",M,[a("header",B,[a("button",{onClick:t[0]||(t[0]=i=>d.$router.back()),class:"back-button","aria-label":"返回"},t[1]||(t[1]=[a("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"currentColor"},[a("path",{d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"})],-1)])),t[2]||(t[2]=a("h1",{class:"page-title"},"认知测试",-1)),t[3]||(t[3]=a("div",{class:"header-spacer"},null,-1))]),a("main",L,[t[7]||(t[7]=h('<section class="test-intro" data-v-3da5571a><div class="intro-card" data-v-3da5571a><div class="intro-content" data-v-3da5571a><h2 class="intro-title" data-v-3da5571a>认知能力评估</h2><p class="intro-description" data-v-3da5571a> 我们的认知测试套件旨在全面评估您的记忆力、注意力、思维能力等多个认知维度。 每个测试都经过科学验证，能够为您提供准确的认知能力分析。 </p></div><div class="intro-illustration" data-v-3da5571a><svg width="80" height="80" viewBox="0 0 80 80" class="illustration-svg" data-v-3da5571a><circle cx="40" cy="40" r="35" fill="var(--color-primary)" opacity="0.1" data-v-3da5571a></circle><path d="M25 40c0-8.284 6.716-15 15-15s15 6.716 15 15" stroke="var(--color-primary)" stroke-width="2" fill="none" data-v-3da5571a></path><circle cx="32" cy="35" r="2" fill="var(--color-primary)" data-v-3da5571a></circle><circle cx="48" cy="35" r="2" fill="var(--color-primary)" data-v-3da5571a></circle><path d="M30 50c0-5.523 4.477-10 10-10s10 4.477 10 10" stroke="var(--color-primary)" stroke-width="2" fill="none" data-v-3da5571a></path></svg></div></div></section>',1)),a("section",H,[t[6]||(t[6]=a("h2",{class:"section-title"},"可用测试项目",-1)),a("div",V,[(s(!0),l(u,null,p(m.value,i=>(s(),l("div",{key:i.id,class:n(["test-card",{disabled:!i.available}]),onClick:r=>v(i),role:"button",tabindex:i.available?0:-1,"aria-disabled":!i.available,onKeydown:[g(r=>v(i),["enter"]),g(r=>v(i),["space"])]},[a("div",T,[a("div",{class:n(["test-icon",`icon-${i.type}`])},[(s(),C(k(b(i.type))))],2),i.available?z("",!0):(s(),l("div",D," 即将开放 "))]),a("div",$,[a("h3",N,o(i.name),1),a("p",P,o(i.description),1),a("div",Q,[a("div",q,[t[4]||(t[4]=a("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"currentColor"},[a("path",{d:"M7 0C3.134 0 0 3.134 0 7s3.134 7 7 7 7-3.134 7-7-3.134-7-7-7zm0 12.6C3.685 12.6 1.4 10.315 1.4 7S3.685 1.4 7 1.4 12.6 3.685 12.6 7 10.315 12.6 7 12.6z"}),a("path",{d:"M7.7 3.5H6.3v4.2l3.15 1.89.7-1.155L7.7 6.65V3.5z"})],-1)),f(" "+o(i.duration),1)]),a("div",K,[t[5]||(t[5]=a("svg",{width:"14",height:"14",viewBox:"0 0 14 14",fill:"currentColor"},[a("path",{d:"M7 1C3.686 1 1 3.686 1 7s2.686 6 6 6 6-2.686 6-6-2.686-6-6-6zm2.5 7.5L7 10V4.5h1.5V8.5z"})],-1)),f(" "+o(i.difficulty),1)])]),a("div",F,[(s(!0),l(u,null,p(i.features,r=>(s(),l("span",{key:r,class:"feature-tag"},o(r),1))),128))])]),a("div",I,[a("button",{disabled:!i.available,class:n(["start-btn",{disabled:!i.available}]),"aria-label":`开始${i.name}测试`},o(i.available?"开始测试":"即将开放"),11,E)])],42,S))),128))])]),t[8]||(t[8]=h('<section class="guidelines-section" data-v-3da5571a><div class="guidelines-card" data-v-3da5571a><h3 class="guidelines-title" data-v-3da5571a>测试须知</h3><div class="guidelines-content" data-v-3da5571a><div class="guideline-item" data-v-3da5571a><div class="guideline-icon" data-v-3da5571a><svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor" data-v-3da5571a><path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" data-v-3da5571a></path></svg></div><div class="guideline-text" data-v-3da5571a><h4 data-v-3da5571a>测试环境</h4><p data-v-3da5571a>请在安静、不被打扰的环境中进行测试，确保网络连接稳定</p></div></div><div class="guideline-item" data-v-3da5571a><div class="guideline-icon" data-v-3da5571a><svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor" data-v-3da5571a><path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" data-v-3da5571a></path></svg></div><div class="guideline-text" data-v-3da5571a><h4 data-v-3da5571a>注意力集中</h4><p data-v-3da5571a>测试过程中请保持专注，避免同时进行其他活动</p></div></div><div class="guideline-item" data-v-3da5571a><div class="guideline-icon" data-v-3da5571a><svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor" data-v-3da5571a><path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" data-v-3da5571a></path></svg></div><div class="guideline-text" data-v-3da5571a><h4 data-v-3da5571a>真实作答</h4><p data-v-3da5571a>请根据真实感受和第一直觉作答，无需过度思考</p></div></div><div class="guideline-item" data-v-3da5571a><div class="guideline-icon" data-v-3da5571a><svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor" data-v-3da5571a><path d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" data-v-3da5571a></path></svg></div><div class="guideline-text" data-v-3da5571a><h4 data-v-3da5571a>完整完成</h4><p data-v-3da5571a>为了获得准确的评估结果，请尽量完成整个测试</p></div></div></div></div></section>',1))])]))}}),G=_(R,[["__scopeId","data-v-3da5571a"]]);export{G as default};
