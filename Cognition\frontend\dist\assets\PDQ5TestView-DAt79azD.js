import{d as Y,r as p,c as m,I as ee,m as te,b as n,e,f as b,h as E,t as r,s as se,a6 as oe,n as S,F,x as N,k as ne,a as le,l,_ as ie}from"./index-tHlaP7TV.js";import{S as ae,a as re,s as k}from"./speech-BAaOvQQ9.js";import{a as ue,G as I}from"./GlobalTimer-BPkfVOeW.js";const ce={class:"test-container"},de={class:"header-section"},ve={class:"header-center"},he={class:"progress-info"},pe={class:"test-title"},_e={class:"progress-bar"},ke={class:"progress-text"},fe={class:"header-right"},we={class:"test-content"},me={key:0,class:"section-intro"},ge={class:"section-card"},xe={class:"section-header"},be={class:"section-title"},Ce=["disabled"],Se={key:0,width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},Ie={key:1,width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},ye={class:"section-description"},Le={class:"section-instructions"},qe={class:"options-preview"},Me={class:"options-list"},Qe={class:"option-value"},Ve={class:"option-text"},Te={key:1,class:"question-content"},je={class:"question-card"},Be={class:"question-header"},De={class:"question-number"},He={key:0,width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},Ae={key:1,width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},Ee={class:"question-text"},Fe={class:"options-container"},Ne=["onClick"],Pe={class:"option-radio"},Ze={key:0,class:"radio-dot"},$e={class:"option-content"},ze={class:"option-main"},Ge={class:"option-value"},Re={class:"option-text"},Ue={class:"question-actions"},Je=["disabled"],Ke=["disabled"],Oe=Y({__name:"PDQ5TestView",setup(We){const y=le(),h=p(0),u=p(0),c=p(null),f=p([]),_=p(!0),C=p(!1),i=p(!1),L=p(ae.isSupported()),P=p(!1),w=re,Z=w.totalQuestions,a=m(()=>w.sections[h.value]),d=m(()=>{if(!a.value||_.value)return null;const s=u.value-g(h.value);return a.value.questions[s]}),$=m(()=>{if(!a.value)return 0;const s=g(h.value),t=u.value-s,o=a.value.questions.length;return Math.round(t/o*100)}),q=m(()=>u.value===Z-1),z=m(()=>{if(!a.value||_.value)return 0;const s=g(h.value);return u.value-s+1}),g=s=>{let t=0;for(let o=0;o<s;o++)t+=w.sections[o].questions.length;return t},G=()=>{_.value=!1},R=s=>{c.value=s,setTimeout(()=>{c.value===s&&M()},500)},U=async()=>{if(a.value)if(i.value)k.stop(),i.value=!1;else try{i.value=!0;const s=`${a.value.description}。${a.value.instructions}`;await k.speak(s),i.value=!1}catch(s){console.error("语音播报失败:",s),i.value=!1}},J=async()=>{if(d.value)if(i.value)k.stop(),i.value=!1;else try{i.value=!0,await k.speak(d.value.text),i.value=!1}catch(s){console.error("语音播报失败:",s),i.value=!1}},M=()=>{if(c.value===null)return;if(d.value){const t=f.value.findIndex(x=>x.questionId===d.value.id),o={questionId:d.value.id,value:c.value,timestamp:Date.now()};t>=0?f.value[t]=o:f.value.push(o)}if(q.value){O();return}u.value++,c.value=null;const s=Q(u.value);s!==h.value&&(h.value=s,_.value=!0)},K=()=>{if(u.value===0)return;u.value--;const s=Q(u.value);if(s!==h.value&&(h.value=s,_.value=!1),d.value){const t=f.value.find(o=>o.questionId===d.value.id);c.value=(t==null?void 0:t.value)||null}},Q=s=>{let t=0;for(let o=0;o<w.sections.length;o++)if(t+=w.sections[o].questions.length,s<t)return o;return w.sections.length-1},O=()=>{k.stop(),console.log("测试完成，答案：",f.value),console.log("测试用时：",I.getFormattedTime()),I.stop(),y.push("/home")},W=()=>{C.value=!0},V=()=>{C.value=!1},X=()=>{I.stop(),k.stop(),y.push("/home")};return ee(d,s=>{if(s&&!_.value){const t=f.value.find(o=>o.questionId===s.id);c.value=(t==null?void 0:t.value)||null}}),te(()=>{k.stop()}),(s,t)=>{var o,x,T,j,B,D,H,A;return l(),n("div",ce,[e("div",de,[e("button",{onClick:W,class:"exit-button","aria-label":"退出测试"},t[1]||(t[1]=[e("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[e("path",{d:"M18 6L6 18M6 6L18 18",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)])),e("div",ve,[E(ue,{position:"title-left",size:"medium"}),e("div",he,[e("h1",pe,r(((o=a.value)==null?void 0:o.name)||"PDQ-5测试"),1),e("div",_e,[e("div",{class:"progress-fill",style:se({width:`${$.value}%`})},null,4)]),e("div",ke,r(u.value-g(h.value)+1)+" / "+r(((x=a.value)==null?void 0:x.questions.length)||0)+" 题 ",1)])]),e("div",fe,[E(oe)])]),e("div",we,[_.value?(l(),n("div",me,[e("div",ge,[e("div",xe,[e("h2",be,r((T=a.value)==null?void 0:T.name),1),L.value?(l(),n("button",{key:0,onClick:U,class:S(["speech-button",{active:i.value}]),disabled:P.value,"aria-label":"语音播报"},[i.value?(l(),n("svg",Ie,t[3]||(t[3]=[e("path",{d:"M11 5L6 9H2V15H6L11 19V5Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),e("path",{d:"M23 9L17 15M17 9L23 15",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))):(l(),n("svg",Se,t[2]||(t[2]=[e("path",{d:"M11 5L6 9H2V15H6L11 19V5Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),e("path",{d:"M19.07 4.93A10 10 0 0 1 19.07 19.07M15.54 8.46A5 5 0 0 1 15.54 15.54",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)])))],10,Ce)):b("",!0)]),e("p",ye,r((j=a.value)==null?void 0:j.description),1),e("div",Le,[e("p",null,r((B=a.value)==null?void 0:B.instructions),1)]),e("div",qe,[t[4]||(t[4]=e("h3",{class:"options-title"},"评分标准：",-1)),e("div",Me,[(l(!0),n(F,null,N((D=a.value)==null?void 0:D.options,v=>(l(),n("div",{key:v.value,class:"option-preview"},[e("span",Qe,r(v.value),1),e("span",Ve,r(v.text),1)]))),128))])]),e("button",{onClick:G,class:"start-section-button"}," 开始测试 ")])])):(l(),n("div",Te,[e("div",je,[e("div",Be,[e("div",De,"第 "+r(z.value)+" 题",1),L.value?(l(),n("button",{key:0,onClick:J,class:S(["speech-button",{active:i.value}]),"aria-label":"语音播报题目"},[i.value?(l(),n("svg",Ae,t[6]||(t[6]=[e("path",{d:"M11 5L6 9H2V15H6L11 19V5Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),e("path",{d:"M23 9L17 15M17 9L23 15",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))):(l(),n("svg",He,t[5]||(t[5]=[e("path",{d:"M11 5L6 9H2V15H6L11 19V5Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),e("path",{d:"M19.07 4.93A10 10 0 0 1 19.07 19.07M15.54 8.46A5 5 0 0 1 15.54 15.54",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)])))],2)):b("",!0)]),e("div",Ee,r((H=d.value)==null?void 0:H.text),1),e("div",Fe,[(l(!0),n(F,null,N((A=d.value)==null?void 0:A.options,v=>(l(),n("div",{key:v.value,class:S(["option-item",{selected:c.value===v.value}]),onClick:Xe=>R(v.value)},[e("div",Pe,[c.value===v.value?(l(),n("div",Ze)):b("",!0)]),e("div",$e,[e("div",ze,[e("span",Ge,r(v.value),1),e("span",Re,r(v.text),1)])])],10,Ne))),128))]),e("div",Ue,[e("button",{onClick:K,class:"nav-button prev-button",disabled:u.value===0}," 上一题 ",8,Je),e("button",{onClick:M,class:"nav-button next-button",disabled:c.value===null},r(q.value?"完成测试":"下一题"),9,Ke)])])]))]),C.value?(l(),n("div",{key:0,class:"modal-overlay",onClick:V},[e("div",{class:"modal-content",onClick:t[0]||(t[0]=ne(()=>{},["stop"]))},[t[7]||(t[7]=e("h3",{class:"modal-title"},"确认退出测试？",-1)),t[8]||(t[8]=e("p",{class:"modal-message"},"退出后当前进度将会丢失，确定要退出吗？",-1)),e("div",{class:"modal-actions"},[e("button",{onClick:V,class:"modal-button cancel-button"},"取消"),e("button",{onClick:X,class:"modal-button confirm-button"},"确认退出")])])])):b("",!0)])}}}),st=ie(Oe,[["__scopeId","data-v-a9bc381e"]]);export{st as default};
