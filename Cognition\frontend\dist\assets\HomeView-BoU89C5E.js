import{m as Ot,G as It,H as Ne,I as U,o as _t,q as re,J as ce,B as ht,r as C,K as ue,L as Mt,d as W,c as q,h as k,M as Fe,N as Q,O as Ye,T as de,P as ve,w as me,Q as fe,F as j,R as He,S as je,u as Ve,b as p,e,f as B,t as r,i as mt,j as Ue,g as nt,U as Gt,x as G,V as Zt,n as F,k as St,a as We,W as Ke,X as Xe,Y as Ge,Z as Ze,$ as qe,l as h,s as Qe,y as Je,a0 as ts,a1 as es,a2 as Bt,_ as ss}from"./index-tHlaP7TV.js";import{d as ot}from"./dayjs.min-CxMP4GVf.js";function ns(){}const H=Object.assign,pe=typeof window<"u",yt=t=>t!==null&&typeof t=="object",Y=t=>t!=null,Pt=t=>typeof t=="function",os=t=>yt(t)&&Pt(t.then)&&Pt(t.catch),he=t=>typeof t=="number"||/^\d+(\.\d+)?$/.test(t),as=()=>pe?/ios|iphone|ipad|ipod/.test(navigator.userAgent.toLowerCase()):!1;function qt(t,s){const a=s.split(".");let i=t;return a.forEach(c=>{var u;i=yt(i)&&(u=i[c])!=null?u:""}),i}function is(t,s,a){return s.reduce((i,c)=>(i[c]=t[c],i),{})}const gt=null,I=[Number,String],V={type:Boolean,default:!0},ls=t=>({type:Number,default:t}),L=t=>({type:String,default:t});var zt=typeof window<"u";function ge(t){let s;_t(()=>{t(),re(()=>{s=!0})}),ce(()=>{s&&t()})}function _e(t,s,a={}){if(!zt)return;const{target:i=window,passive:c=!1,capture:u=!1}=a;let d=!1,l;const v=f=>{if(d)return;const _=ht(f);_&&!l&&(_.addEventListener(t,s,{capture:u,passive:c}),l=!0)},b=f=>{if(d)return;const _=ht(f);_&&l&&(_.removeEventListener(t,s,u),l=!1)};Ot(()=>b(i)),It(()=>b(i)),ge(()=>v(i));let w;return Ne(i)&&(w=U(i,(f,_)=>{b(_),v(f)})),()=>{w==null||w(),b(i),d=!0}}var ft,Dt;function rs(){if(!ft&&(ft=C(0),Dt=C(0),zt)){const t=()=>{ft.value=window.innerWidth,Dt.value=window.innerHeight};t(),window.addEventListener("resize",t,{passive:!0}),window.addEventListener("orientationchange",t,{passive:!0})}return{width:ft,height:Dt}}var cs=/scroll|auto|overlay/i,us=zt?window:void 0;function ds(t){return t.tagName!=="HTML"&&t.tagName!=="BODY"&&t.nodeType===1}function vs(t,s=us){let a=t;for(;a&&a!==s&&ds(a);){const{overflowY:i}=window.getComputedStyle(a);if(cs.test(i))return a;a=a.parentNode}return s}as();const ms=t=>t.stopPropagation();function ye(t,s){(typeof t.cancelable!="boolean"||t.cancelable)&&t.preventDefault(),ms(t)}rs();function R(t){if(Y(t))return he(t)?`${t}px`:String(t)}function fs(t){if(Y(t)){if(Array.isArray(t))return{width:R(t[0]),height:R(t[1])};const s=R(t);return{width:s,height:s}}}function ps(t){const s={};return t!==void 0&&(s.zIndex=+t),s}const hs=/-(\w)/g,be=t=>t.replace(hs,(s,a)=>a.toUpperCase()),{hasOwnProperty:gs}=Object.prototype;function _s(t,s,a){const i=s[a];Y(i)&&(!gs.call(t,a)||!yt(i)?t[a]=i:t[a]=ke(Object(t[a]),i))}function ke(t,s){return Object.keys(s).forEach(a=>{_s(t,s,a)}),t}var ys={name:"姓名",tel:"电话",save:"保存",clear:"清空",cancel:"取消",confirm:"确认",delete:"删除",loading:"加载中...",noCoupon:"暂无优惠券",nameEmpty:"请填写姓名",addContact:"添加联系人",telInvalid:"请填写正确的电话",vanCalendar:{end:"结束",start:"开始",title:"日期选择",weekdays:["日","一","二","三","四","五","六"],monthTitle:(t,s)=>`${t}年${s}月`,rangePrompt:t=>`最多选择 ${t} 天`},vanCascader:{select:"请选择"},vanPagination:{prev:"上一页",next:"下一页"},vanPullRefresh:{pulling:"下拉即可刷新...",loosing:"释放即可刷新..."},vanSubmitBar:{label:"合计:"},vanCoupon:{unlimited:"无门槛",discount:t=>`${t}折`,condition:t=>`满${t}元可用`},vanCouponCell:{title:"优惠券",count:t=>`${t}张可用`},vanCouponList:{exchange:"兑换",close:"不使用",enable:"可用",disabled:"不可用",placeholder:"输入优惠码"},vanAddressEdit:{area:"地区",areaEmpty:"请选择地区",addressEmpty:"请填写详细地址",addressDetail:"详细地址",defaultAddress:"设为默认收货地址"},vanAddressList:{add:"新增地址"}};const Qt=C("zh-CN"),Jt=ue({"zh-CN":ys}),bs={messages(){return Jt[Qt.value]},use(t,s){Qt.value=t,this.add({[t]:s})},add(t={}){ke(Jt,t)}};var ks=bs;function ws(t){const s=be(t)+".";return(a,...i)=>{const c=ks.messages(),u=qt(c,s+a)||qt(c,a);return Pt(u)?u(...i):u}}function $t(t,s){return s?typeof s=="string"?` ${t}--${s}`:Array.isArray(s)?s.reduce((a,i)=>a+$t(t,i),""):Object.keys(s).reduce((a,i)=>a+(s[i]?$t(t,i):""),""):""}function Cs(t){return(s,a)=>(s&&typeof s!="string"&&(a=s,s=""),s=s?`${t}__${s}`:t,`${s}${$t(s,a)}`)}function K(t){const s=`van-${t}`;return[s,Cs(s),ws(s)]}const Ts="van-haptics-feedback",te=5;function xs(t,{args:s=[],done:a,canceled:i,error:c}){if(t){const u=t.apply(null,s);os(u)?u.then(d=>{d?a():i&&i()}).catch(c||ns):u?a():i&&i()}else a()}function J(t){return t.install=s=>{const{name:a}=t;a&&(s.component(a,t),s.component(be(`-${a}`),t))},t}const Es=Symbol();function we(t){const s=Mt();s&&H(s.proxy,t)}const[Ss,ee]=K("badge"),Bs={dot:Boolean,max:I,tag:L("div"),color:String,offset:Array,content:I,showZero:V,position:L("top-right")};var Ds=W({name:Ss,props:Bs,setup(t,{slots:s}){const a=()=>{if(s.content)return!0;const{content:l,showZero:v}=t;return Y(l)&&l!==""&&(v||l!==0&&l!=="0")},i=()=>{const{dot:l,max:v,content:b}=t;if(!l&&a())return s.content?s.content():Y(v)&&he(b)&&+b>+v?`${v}+`:b},c=l=>l.startsWith("-")?l.replace("-",""):`-${l}`,u=q(()=>{const l={background:t.color};if(t.offset){const[v,b]=t.offset,{position:w}=t,[f,_]=w.split("-");s.default?(typeof b=="number"?l[f]=R(f==="top"?b:-b):l[f]=f==="top"?R(b):c(b),typeof v=="number"?l[_]=R(_==="left"?v:-v):l[_]=_==="left"?R(v):c(v)):(l.marginTop=R(b),l.marginLeft=R(v))}return l}),d=()=>{if(a()||t.dot)return k("div",{class:ee([t.position,{dot:t.dot,fixed:!!s.default}]),style:u.value},[i()])};return()=>{if(s.default){const{tag:l}=t;return k(l,{class:ee("wrapper")},{default:()=>[s.default(),d()]})}return d()}}});const Ps=J(Ds);let $s=2e3;const Os=()=>++$s,[Is,Wa]=K("config-provider"),Ms=Symbol(Is),[zs,se]=K("icon"),As=t=>t==null?void 0:t.includes("/"),Rs={dot:Boolean,tag:L("i"),name:String,size:I,badge:I,color:String,badgeProps:Object,classPrefix:String};var Ls=W({name:zs,props:Rs,setup(t,{slots:s}){const a=Fe(Ms,null),i=q(()=>t.classPrefix||(a==null?void 0:a.iconPrefix)||se());return()=>{const{tag:c,dot:u,name:d,size:l,badge:v,color:b}=t,w=As(d);return k(Ps,Q({dot:u,tag:c,class:[i.value,w?"":`${i.value}-${d}`],style:{color:b,fontSize:R(l)},content:v},t.badgeProps),{default:()=>{var f;return[(f=s.default)==null?void 0:f.call(s),w&&k("img",{class:se("image"),src:d},null)]}})}}});const Ce=J(Ls),[Ns,lt]=K("loading"),Fs=Array(12).fill(null).map((t,s)=>k("i",{class:lt("line",String(s+1))},null)),Ys=k("svg",{class:lt("circular"),viewBox:"25 25 50 50"},[k("circle",{cx:"50",cy:"50",r:"20",fill:"none"},null)]),Hs={size:I,type:L("circular"),color:String,vertical:Boolean,textSize:I,textColor:String};var js=W({name:Ns,props:Hs,setup(t,{slots:s}){const a=q(()=>H({color:t.color},fs(t.size))),i=()=>{const u=t.type==="spinner"?Fs:Ys;return k("span",{class:lt("spinner",t.type),style:a.value},[s.icon?s.icon():u])},c=()=>{var u;if(s.default)return k("span",{class:lt("text"),style:{fontSize:R(t.textSize),color:(u=t.textColor)!=null?u:t.color}},[s.default()])};return()=>{const{type:u,vertical:d}=t;return k("div",{class:lt([u,{vertical:d}]),"aria-live":"polite","aria-busy":!0},[i(),c()])}}});const Vs=J(js),Us={show:Boolean,zIndex:I,overlay:V,duration:I,teleport:[String,Object],lockScroll:V,lazyRender:V,beforeClose:Function,overlayProps:Object,overlayStyle:Object,overlayClass:gt,transitionAppear:Boolean,closeOnClickOverlay:V};function Ws(t,s){return t>s?"horizontal":s>t?"vertical":""}function Ks(){const t=C(0),s=C(0),a=C(0),i=C(0),c=C(0),u=C(0),d=C(""),l=C(!0),v=()=>d.value==="vertical",b=()=>d.value==="horizontal",w=()=>{a.value=0,i.value=0,c.value=0,u.value=0,d.value="",l.value=!0};return{move:D=>{const g=D.touches[0];a.value=(g.clientX<0?0:g.clientX)-t.value,i.value=g.clientY-s.value,c.value=Math.abs(a.value),u.value=Math.abs(i.value);const z=10;(!d.value||c.value<z&&u.value<z)&&(d.value=Ws(c.value,u.value)),l.value&&(c.value>te||u.value>te)&&(l.value=!1)},start:D=>{w(),t.value=D.touches[0].clientX,s.value=D.touches[0].clientY},reset:w,startX:t,startY:s,deltaX:a,deltaY:i,offsetX:c,offsetY:u,direction:d,isVertical:v,isHorizontal:b,isTap:l}}let at=0;const ne="van-overflow-hidden";function Xs(t,s){const a=Ks(),i="01",c="10",u=w=>{a.move(w);const f=a.deltaY.value>0?c:i,_=vs(w.target,t.value),{scrollHeight:D,offsetHeight:g,scrollTop:z}=_;let M="11";z===0?M=g>=D?"00":"01":z+g>=D&&(M="10"),M!=="11"&&a.isVertical()&&!(parseInt(M,2)&parseInt(f,2))&&ye(w)},d=()=>{document.addEventListener("touchstart",a.start),document.addEventListener("touchmove",u,{passive:!1}),at||document.body.classList.add(ne),at++},l=()=>{at&&(document.removeEventListener("touchstart",a.start),document.removeEventListener("touchmove",u),at--,at||document.body.classList.remove(ne))},v=()=>s()&&d(),b=()=>s()&&l();ge(v),It(b),Ye(b),U(s,w=>{w?d():l()})}function Te(t){const s=C(!1);return U(t,a=>{a&&(s.value=a)},{immediate:!0}),a=>()=>s.value?a():null}const oe=()=>{var t;const{scopeId:s}=((t=Mt())==null?void 0:t.vnode)||{};return s?{[s]:""}:null},[Gs,Zs]=K("overlay"),qs={show:Boolean,zIndex:I,duration:I,className:gt,lockScroll:V,lazyRender:V,customStyle:Object,teleport:[String,Object]};var Qs=W({name:Gs,inheritAttrs:!1,props:qs,setup(t,{attrs:s,slots:a}){const i=C(),c=Te(()=>t.show||!t.lazyRender),u=l=>{t.lockScroll&&ye(l)},d=c(()=>{var l;const v=H(ps(t.zIndex),t.customStyle);return Y(t.duration)&&(v.animationDuration=`${t.duration}s`),me(k("div",Q({ref:i,style:v,class:[Zs(),t.className]},s),[(l=a.default)==null?void 0:l.call(a)]),[[fe,t.show]])});return _e("touchmove",u,{target:i}),()=>{const l=k(de,{name:"van-fade",appear:!0},{default:d});return t.teleport?k(ve,{to:t.teleport},{default:()=>[l]}):l}}});const Js=J(Qs),tn=H({},Us,{round:Boolean,position:L("center"),closeIcon:L("cross"),closeable:Boolean,transition:String,iconPrefix:String,closeOnPopstate:Boolean,closeIconPosition:L("top-right"),destroyOnClose:Boolean,safeAreaInsetTop:Boolean,safeAreaInsetBottom:Boolean}),[en,ae]=K("popup");var sn=W({name:en,inheritAttrs:!1,props:tn,emits:["open","close","opened","closed","keydown","update:show","clickOverlay","clickCloseIcon"],setup(t,{emit:s,attrs:a,slots:i}){let c,u;const d=C(),l=C(),v=Te(()=>t.show||!t.lazyRender),b=q(()=>{const E={zIndex:d.value};if(Y(t.duration)){const A=t.position==="center"?"animationDuration":"transitionDuration";E[A]=`${t.duration}s`}return E}),w=()=>{c||(c=!0,d.value=t.zIndex!==void 0?+t.zIndex:Os(),s("open"))},f=()=>{c&&xs(t.beforeClose,{done(){c=!1,s("close"),s("update:show",!1)}})},_=E=>{s("clickOverlay",E),t.closeOnClickOverlay&&f()},D=()=>{if(t.overlay){const E=H({show:t.show,class:t.overlayClass,zIndex:d.value,duration:t.duration,customStyle:t.overlayStyle,role:t.closeOnClickOverlay?"button":void 0,tabindex:t.closeOnClickOverlay?0:void 0},t.overlayProps);return k(Js,Q(E,oe(),{onClick:_}),{default:i["overlay-content"]})}},g=E=>{s("clickCloseIcon",E),f()},z=()=>{if(t.closeable)return k(Ce,{role:"button",tabindex:0,name:t.closeIcon,class:[ae("close-icon",t.closeIconPosition),Ts],classPrefix:t.iconPrefix,onClick:g},null)};let M;const P=()=>{M&&clearTimeout(M),M=setTimeout(()=>{s("opened")})},bt=()=>s("closed"),kt=E=>s("keydown",E),wt=v(()=>{var E;const{destroyOnClose:A,round:tt,position:X,safeAreaInsetTop:Ct,safeAreaInsetBottom:Tt,show:ct}=t;if(!(!ct&&A))return me(k("div",Q({ref:l,style:b.value,role:"dialog",tabindex:0,class:[ae({round:tt,[X]:X}),{"van-safe-area-top":Ct,"van-safe-area-bottom":Tt}],onKeydown:kt},a,oe()),[(E=i.default)==null?void 0:E.call(i),z()]),[[fe,ct]])}),rt=()=>{const{position:E,transition:A,transitionAppear:tt}=t,X=E==="center"?"van-fade":`van-popup-slide-${E}`;return k(de,{name:A||X,appear:tt,onAfterEnter:P,onAfterLeave:bt},{default:wt})};return U(()=>t.show,E=>{E&&!c&&(w(),a.tabindex===0&&re(()=>{var A;(A=l.value)==null||A.focus()})),!E&&c&&(c=!1,s("close"))}),we({popupRef:l}),Xs(l,()=>t.show&&t.lockScroll),_e("popstate",()=>{t.closeOnPopstate&&(f(),u=!1)}),_t(()=>{t.show&&w()}),ce(()=>{u&&(s("update:show",!0),u=!1)}),It(()=>{t.show&&t.teleport&&(f(),u=!0)}),He(Es,()=>t.show),()=>t.teleport?k(ve,{to:t.teleport},{default:()=>[D(),rt()]}):k(j,null,[D(),rt()])}});const nn=J(sn);let it=0;function on(t){t?(it||document.body.classList.add("van-toast--unclickable"),it++):it&&(it--,it||document.body.classList.remove("van-toast--unclickable"))}const[an,Z]=K("toast"),ln=["show","overlay","teleport","transition","overlayClass","overlayStyle","closeOnClickOverlay","zIndex"],rn={icon:String,show:Boolean,type:L("text"),overlay:Boolean,message:I,iconSize:I,duration:ls(2e3),position:L("middle"),teleport:[String,Object],wordBreak:String,className:gt,iconPrefix:String,transition:L("van-fade"),loadingType:String,forbidClick:Boolean,overlayClass:gt,overlayStyle:Object,closeOnClick:Boolean,closeOnClickOverlay:Boolean,zIndex:I};var xe=W({name:an,props:rn,emits:["update:show"],setup(t,{emit:s,slots:a}){let i,c=!1;const u=()=>{const f=t.show&&t.forbidClick;c!==f&&(c=f,on(c))},d=f=>s("update:show",f),l=()=>{t.closeOnClick&&d(!1)},v=()=>clearTimeout(i),b=()=>{const{icon:f,type:_,iconSize:D,iconPrefix:g,loadingType:z}=t;if(f||_==="success"||_==="fail")return k(Ce,{name:f||_,size:D,class:Z("icon"),classPrefix:g},null);if(_==="loading")return k(Vs,{class:Z("loading"),size:D,type:z},null)},w=()=>{const{type:f,message:_}=t;if(a.message)return k("div",{class:Z("text")},[a.message()]);if(Y(_)&&_!=="")return f==="html"?k("div",{key:0,class:Z("text"),innerHTML:String(_)},null):k("div",{class:Z("text")},[_])};return U(()=>[t.show,t.forbidClick],u),U(()=>[t.show,t.type,t.message,t.duration],()=>{v(),t.show&&t.duration>0&&(i=setTimeout(()=>{d(!1)},t.duration))}),_t(u),Ot(u),()=>k(nn,Q({class:[Z([t.position,t.wordBreak==="normal"?"break-normal":t.wordBreak,{[t.type]:!t.icon}]),t.className],lockScroll:!1,onClick:l,onClosed:v,"onUpdate:show":d},is(t,ln)),{default:()=>[b(),w()]})}});function cn(){const t=ue({show:!1}),s=c=>{t.show=c},a=c=>{H(t,c,{transitionAppear:!0}),s(!0)},i=()=>s(!1);return we({open:a,close:i,toggle:s}),{open:a,close:i,state:t,toggle:s}}function un(t){const s=je(t),a=document.createElement("div");return document.body.appendChild(a),{instance:s.mount(a),unmount(){s.unmount(),document.body.removeChild(a)}}}const dn={icon:"",type:"text",message:"",className:"",overlay:!1,onClose:void 0,onOpened:void 0,duration:2e3,teleport:"body",iconSize:void 0,iconPrefix:void 0,position:"middle",transition:"van-fade",forbidClick:!1,loadingType:void 0,overlayClass:"",overlayStyle:void 0,closeOnClick:!1,closeOnClickOverlay:!1};let pt=[],vn=!1,ie=H({},dn);const mn=new Map;function fn(t){return yt(t)?t:{message:t}}function pn(){const{instance:t}=un({setup(){const s=C(""),{open:a,state:i,close:c,toggle:u}=cn(),d=()=>{},l=()=>k(xe,Q(i,{onClosed:d,"onUpdate:show":u}),null);return U(s,v=>{i.message=v}),Mt().render=l,{open:a,close:c,message:s}}});return t}function hn(){if(!pt.length||vn){const t=pn();pt.push(t)}return pt[pt.length-1]}function le(t={}){if(!pe)return{};const s=hn(),a=fn(t);return s.open(H({},ie,mn.get(a.type||ie.type),a)),s}J(xe);const gn={class:"home-container"},_n={class:"top-nav"},yn={class:"nav-content"},bn={class:"user-info"},kn={class:"avatar"},wn=["src","alt"],Cn={class:"user-details"},Tn={class:"user-name"},xn={class:"user-id"},En={class:"menu-wrapper"},Sn=["aria-expanded"],Bn={class:"main-content"},Dn={class:"welcome-section"},Pn={class:"greeting-header"},$n={class:"greeting-text"},On={class:"greeting-title"},In={class:"quick-stats"},Mn={class:"stat-item"},zn={class:"stat-value"},An={class:"stat-item"},Rn={class:"stat-value"},Ln={class:"stat-label"},Nn={class:"stat-item"},Fn={class:"stat-value"},Yn={class:"test-tasks"},Hn={class:"section-header"},jn={class:"section-title"},Vn={class:"task-count"},Un={class:"test-suites-accordion"},Wn=["onClick"],Kn={class:"suite-main-info"},Xn={class:"suite-title-section"},Gn={class:"suite-name"},Zn={class:"suite-meta"},qn={class:"suite-number"},Qn={class:"suite-date"},Jn={class:"suite-actions"},to=["disabled"],eo={class:"suite-progress"},so={class:"suite-progress-bar"},no={class:"suite-progress-text"},oo={key:0,class:"suite-content"},ao={class:"tasks-list"},io=["onClick"],lo={class:"task-basic-info"},ro={class:"task-icon"},co={class:"task-main"},uo={class:"task-name"},vo={class:"task-brief"},mo={class:"task-type"},fo={class:"task-duration"},po={class:"task-status-section"},ho={class:"task-completion-info"},go={class:"task-completion-status"},_o={class:"task-completion-detail"},yo={key:0,class:"task-details"},bo={class:"task-description-section"},ko={class:"task-full-description"},wo={key:0,class:"task-records-section"},Co={class:"records-header"},To={class:"records-title-section"},xo={class:"records-count"},Eo=["onClick","disabled"],So={class:"records-table"},Bo={class:"table-body"},Do={class:"col-start-date"},Po={class:"start-date-info"},$o={class:"col-end-date"},Oo={class:"end-date-info"},Io={class:"col-duration"},Mo={class:"duration-main"},zo={class:"col-action"},Ao=["onClick"],Ro={key:0,class:"more-records"},Lo={class:"test-records"},No={class:"section-header"},Fo={key:0,class:"records-accordion"},Yo=["onClick"],Ho={class:"date-info"},jo={class:"date-title"},Vo={class:"date-count"},Uo={class:"date-stats"},Wo={class:"completion-rate"},Ko={class:"rate-value"},Xo=["onClick"],Go={key:0,class:"date-records"},Zo=["onClick"],qo={class:"record-basic-info"},Qo={class:"record-icon"},Jo={class:"record-main-info"},ta={class:"record-name"},ea={class:"record-meta"},sa={class:"record-time"},na={key:0,class:"record-duration"},oa={key:1,class:"record-score-preview"},aa={class:"record-status-section"},ia={key:0,class:"record-details"},la={key:0,class:"record-summary"},ra={class:"summary-item"},ca={class:"summary-value"},ua={key:0,class:"summary-item"},da={class:"summary-value"},va={key:1,class:"summary-item"},ma={class:"summary-value score-highlight"},fa={key:2,class:"summary-item"},pa={class:"summary-value"},ha={class:"record-actions"},ga=["onClick"],_a=["onClick"],ya={key:1,class:"empty-records"},ba={class:"report-modal-header"},ka={class:"report-modal-title"},wa={class:"report-modal-content"},Ca={key:0,class:"report-details"},Ta={class:"report-metrics"},xa={class:"metric-card score-card"},Ea={class:"metric-info"},Sa={class:"metric-value"},Ba={class:"metric-card percentile-card"},Da={class:"metric-info"},Pa={class:"metric-value"},$a={key:0,class:"metric-card attempts-card"},Oa={class:"metric-info"},Ia={class:"metric-value"},Ma={class:"report-section"},za={class:"interpretation-text"},Aa={class:"report-section"},Ra={class:"comparison-card"},La={class:"comparison-item"},Na={class:"comparison-item"},Fa={class:"comparison-value"},Ya={key:0,class:"report-section"},Ha={class:"recommendations-list"},ja=W({__name:"HomeView",setup(t){const s=We(),a=Ve(),i=C(!1),c=C([]),u=C({}),d=C([]),l=C(!1),v=C(0),b=C(null),w=C(null),f=C(null),_=C(null),D=C(!1),g=C(null),z=()=>{if(!P.value)return;const o=c.value.filter(n=>n.status==="completed").map(n=>M(n.testType));l.value=Ge(o),v.value=Ze("main_cognitive_suite",o),l.value?d.value=qe(o):d.value=Gt()},M=o=>({PDQ5:"task_pdq5",Hopkins:"task_hopkins",NBack:"task_nback",Stroop:"task_stroop",TrailMaking:"task_trail",VerbalFluency:"task_fluency",CPT:"task_cpt",DSST:"task_dsst"})[o]||"",P=q(()=>a.user),bt=q(()=>c.value.filter(o=>o.status==="completed").length),kt=()=>v.value,wt=()=>{const o=new Date().getHours();return o<12?"早上好":o<18?"下午好":"晚上好"},rt=o=>o==="female"?"/images/Female.png":"/images/Male.png",E=o=>({easy:"简单",medium:"中等",hard:"困难"})[o]||o,A=async o=>{if(!o.isAvailable)return;const n=l.value,$=c.value.filter(O=>O.status==="completed").map(O=>M(O.testType)).includes(o.id);let x=`即将开始 ${o.name}

`;if(x+=`• 预计耗时：${o.estimatedDuration} 分钟
`,x+=`• 测试类型：${o.category}
`,x+=`• 难度等级：${E(o.difficulty)}
`,n&&$?(x+=`
🔄 这是一次重测，您之前已完成过此测试。
`,x+=`重测可以帮助您提升成绩和熟练度。
`):n||(x+=`
🏆 这是主要测试的一部分，完成后将统计在整体进度中。
`),x+=`
请确保您有充足的时间完成测试，测试过程中请勿中断。

确定开始吗？`,confirm(x))try{if(console.log("开始测试:",o.name,o.type,n?"(重测)":"(首次)"),o.type==="PDQ5")await s.push("/tests/pdq5/instructions");else{const O=n&&$?"重测":"首次测试";alert(`${o.name} (${O}) 页面正在开发中...

即将跳转到测试列表页面查看所有可用测试。`),await s.push("/tests")}}catch(O){console.error("启动测试失败:",O),alert("启动测试失败，请稍后重试")}},tt=o=>({PDQ5:"🧠",Hopkins:"📚",NBack:"🔢",Stroop:"🎨",TrailMaking:"🔗",VerbalFluency:"💬",CPT:"⏱️",DSST:"🔢"})[o]||"📝",X=o=>({pending:"待开始",inProgress:"进行中",completed:"已完成",failed:"失败"})[o]||o,Ct=async()=>{if(confirm("确定要退出登录吗？"))try{a.logout(),await s.push("/login")}catch(o){console.error("退出登录失败:",o)}},Tt=o=>{f.value=f.value===o?null:o,_.value=null},ct=o=>{_.value=_.value===o?null:o},At=o=>{const n=o.filter(T=>T.status==="completed").length;return o.length>0?Math.round(n/o.length*100):0},Ee=(o,n)=>{const T=n.filter(O=>O.status==="completed");if(T.length===0){le("暂无测试报告");return}const $=T.reduce((O,vt)=>O+(vt.score||0),0),x=Math.round($/T.length),N={isTaskReport:!0,taskName:`${ut(o)} 测试任务`,testName:`${ut(o)} 测试任务`,testCount:T.length,score:x,percentile:Math.floor(Math.random()*40)+60,analysis:`基于 ${T.length} 个测试项目的综合分析显示，您在${ut(o)}的整体表现${x>80?"优秀":x>70?"良好":"中等"}。各项认知能力指标表现均衡。`,recommendations:["恭喜完成当日的认知能力评估","建议定期进行复测以跟踪认知能力变化","可以针对薄弱环节进行专项训练","保持健康的生活方式有助于认知能力维护"]};g.value=N,D.value=!0},Rt=o=>{const n=ts(o);if(!n){le("暂无测试结果");return}n.isTaskReport=!1,g.value=n,D.value=!0},xt=()=>{D.value=!1,g.value=null},Se=o=>{confirm(`继续进行 ${o.testName}？

测试将从上次中断的地方继续。`)&&alert("正在跳转到测试页面...")},ut=o=>ot(o).format("YYYY年MM月DD日"),Be=o=>ot(o).format("HH:mm"),Lt=o=>ot(o).format("YYYY年MM月DD日 HH:mm"),De=()=>{const o=d.value.find(n=>n.isAvailable);o?A(o):alert("暂无可用测试")},Nt=()=>{s.push("/cognitive-assessment")},Pe=o=>{b.value=b.value===o?null:o,w.value=null},$e=o=>{w.value=w.value===o?null:o},Oe=o=>{const n=Zt.find(T=>T.id===o);return n?es.filter(T=>n.tasks.includes(T.id)).sort((T,$)=>T.order-$.order):[]},et=o=>P.value?Bt(P.value.id,o):[],Ie=o=>{if(!P.value)return"未开始";const n=Bt(P.value.id,o),T=n.filter(x=>x.status==="completed");return n.filter(x=>x.status==="inProgress").length>0?"进行中":T.length===0?"未开始":"已完成"},Me=o=>{if(!P.value)return"0%";const n=Bt(P.value.id,o),T=n.filter(x=>x.status==="completed");return n.filter(x=>x.status==="inProgress").length>0?"进行中":T.length===0?"0%":"100%"},ze=o=>o.status==="completed"?"已完成":v.value===0?"立即开始测试":"继续测试",Ae=o=>ot(o).format("MM月DD日"),Ft=o=>ot(o).format("YYYY-MM-DD HH:mm:ss"),Re=o=>`${Math.round(o/60*100)/100}分钟`,Le=()=>{P.value&&(c.value=Ke(P.value.id).sort((o,n)=>new Date(n.startTime).getTime()-new Date(o.startTime).getTime()),u.value=Xe(P.value.id),z())},Yt=()=>{const o=window.innerWidth>=768&&window.innerWidth<=1024,n=window.innerWidth>window.innerHeight,T="ontouchstart"in window||navigator.maxTouchPoints>0;if(o&&n&&T){console.log("🏠 检测到平板横屏设备，开始优化主页卡片布局...");const $="home-page-tablet-optimization";if(!document.getElementById($)){const x=`
        @media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape) {
          .test-suites-accordion {
            display: grid !important;
            grid-template-columns: repeat(auto-fit, minmax(450px, 1fr)) !important;
            gap: 24px !important;
            margin-bottom: 32px !important;
          }

          .suite-accordion-item {
            width: 100% !important;
            max-width: none !important;
            margin-bottom: 0 !important;
          }

          .suite-header {
            padding: 24px !important;
            min-height: 120px !important;
          }

          .suite-main-info {
            flex-direction: column !important;
            gap: 16px !important;
            align-items: flex-start !important;
          }

          .suite-actions {
            align-self: stretch !important;
            justify-content: space-between !important;
          }

          .suite-start-btn {
            flex: 1 !important;
            max-width: 200px !important;
            min-height: 56px !important;
            font-size: 16px !important;
            padding: 16px 24px !important;
          }

          .records-accordion {
            display: grid !important;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)) !important;
            gap: 20px !important;
          }

          .record-date-group {
            margin-bottom: 0 !important;
          }

          .tasks-list {
            display: grid !important;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)) !important;
            gap: 16px !important;
            padding: 24px !important;
          }

          .task-accordion-item {
            margin-bottom: 0 !important;
          }
        }
      `,N=document.createElement("style");N.id=$,N.textContent=x,document.head.appendChild(N),console.log("✅ 主页平板优化CSS已注入")}}};let st=null;const dt=()=>{st&&clearTimeout(st),st=setTimeout(()=>{Yt()},300)};return _t(()=>{Le(),Yt(),window.addEventListener("resize",dt),window.addEventListener("orientationchange",dt),document.addEventListener("click",o=>{(!o.target||!o.target.closest(".user-menu, .menu-button"))&&(i.value=!1)})}),Ot(()=>{window.removeEventListener("resize",dt),window.removeEventListener("orientationchange",dt),st&&clearTimeout(st)}),(o,n)=>{var $,x,N,O,vt,Ht,jt,Vt,Ut,Wt,Kt;const T=Ue("router-link");return h(),p("div",gn,[e("header",_n,[e("div",yn,[e("div",bn,[e("div",kn,[e("img",{src:rt(($=P.value)==null?void 0:$.gender),alt:`${(x=P.value)==null?void 0:x.name}的头像`,class:"avatar-img"},null,8,wn)]),e("div",Cn,[e("h2",Tn,r(((N=P.value)==null?void 0:N.name)||"用户"),1),e("p",xn,r((O=P.value)==null?void 0:O.patientNumber),1)])]),e("div",En,[e("button",{onClick:n[0]||(n[0]=S=>i.value=!i.value),class:"menu-button","aria-expanded":i.value,"aria-label":"用户菜单"},n[4]||(n[4]=[e("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor"},[e("path",{d:"M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"})],-1)]),8,Sn),i.value?(h(),p("div",{key:0,class:"user-menu",onClick:n[1]||(n[1]=S=>i.value=!1)},[k(T,{to:"/profile",class:"menu-item"},{default:mt(()=>n[5]||(n[5]=[e("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"})],-1),nt(" 个人信息 ",-1)])),_:1,__:[5]}),k(T,{to:"/test-history",class:"menu-item"},{default:mt(()=>n[6]||(n[6]=[e("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{d:"M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"})],-1),nt(" 测试记录 ",-1)])),_:1,__:[6]}),e("button",{onClick:Ct,class:"menu-item logout-item"},n[7]||(n[7]=[e("svg",{width:"20",height:"20",viewBox:"0 0 20 20",fill:"currentColor"},[e("path",{d:"M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z"})],-1),nt(" 退出登录 ",-1)]))])):B("",!0)])])]),e("main",Bn,[e("section",Dn,[e("div",Pn,[e("div",$n,[e("h1",On,r(wt())+"，"+r((vt=P.value)==null?void 0:vt.name)+"！ ",1),n[8]||(n[8]=e("p",{class:"greeting-subtitle"}," 欢迎使用认知测试系统，开始您的认知能力评估之旅 ",-1))]),e("div",In,[e("div",Mn,[e("div",zn,r(bt.value),1),n[9]||(n[9]=e("div",{class:"stat-label"},"已完成",-1))]),n[11]||(n[11]=e("div",{class:"stat-divider"},null,-1)),e("div",An,[e("div",Rn,r(l.value?d.value.length:ht(Gt)().length),1),e("div",Ln,r(l.value?"可重测":"主要测试"),1)]),n[12]||(n[12]=e("div",{class:"stat-divider"},null,-1)),e("div",Nn,[e("div",Fn,r(kt())+"%",1),n[10]||(n[10]=e("div",{class:"stat-label"},"完成率",-1))])])])]),e("section",Yn,[e("div",Hn,[e("h2",jn,r((l.value,"📝 测试任务")),1),e("div",Vn,"共 "+r(d.value.length)+" 个测试",1)]),e("div",Un,[(h(!0),p(j,null,G(ht(Zt),S=>(h(),p("div",{key:S.id,class:"suite-accordion-item"},[e("div",{class:"suite-header",onClick:y=>Pe(S.id)},[e("div",Kn,[e("div",Xn,[e("h3",Gn,r(S.name),1),e("div",Zn,[e("span",qn,"编号："+r(S.suiteNumber),1),e("span",Qn,"创建日期："+r(Ae(S.createdDate)),1)])]),e("div",Jn,[e("button",{onClick:St(Nt,["stop"]),class:"suite-start-btn",disabled:S.status==="completed"},r(ze(S)),9,to),e("button",{class:F(["suite-expand-btn",{expanded:b.value===S.id}])},n[13]||(n[13]=[e("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor"},[e("path",{d:"M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"})],-1)]),2)])]),e("div",eo,[e("div",so,[e("div",{class:"suite-progress-fill",style:Qe({width:v.value+"%"})},null,4)]),e("span",no,r(v.value)+"% 已完成",1)])],8,Wn),b.value===S.id?(h(),p("div",oo,[e("div",ao,[(h(!0),p(j,null,G(Oe(S.id),y=>(h(),p("div",{key:y.id,class:"task-accordion-item"},[e("div",{class:"task-header",onClick:m=>$e(y.id)},[e("div",lo,[e("div",ro,r(y.icon),1),e("div",co,[e("h4",uo,r(y.name),1),e("div",vo,[e("span",mo,r(y.category),1),e("span",fo,"约"+r(y.estimatedDuration)+"分钟",1),e("span",{class:F(["task-difficulty-badge",y.difficulty])},r(E(y.difficulty)),3)])])]),e("div",po,[e("div",ho,[e("div",go,r(Ie(y.type)),1),e("div",_o,r(Me(y.type)),1)]),e("button",{class:F(["task-expand-btn",{expanded:w.value===y.id}])},n[14]||(n[14]=[e("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor"},[e("path",{d:"M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"})],-1)]),2)])],8,io),w.value===y.id?(h(),p("div",yo,[e("div",bo,[e("p",ko,r(y.fullDescription||y.description),1)]),et(y.type).length>0?(h(),p("div",wo,[e("div",Co,[e("div",To,[n[15]||(n[15]=e("h5",null,"测试记录",-1)),e("span",xo,"共 "+r(et(y.type).length)+" 次",1)]),e("button",{onClick:m=>A(y),class:"retest-btn",disabled:!y.isAvailable}," 重新测试 ",8,Eo)]),e("div",So,[n[16]||(n[16]=Je('<div class="table-header" data-v-6c8341d0><div class="col-start-date" data-v-6c8341d0>开始日期</div><div class="col-end-date" data-v-6c8341d0>结束日期</div><div class="col-duration" data-v-6c8341d0>测试用时（分钟）</div><div class="col-action" data-v-6c8341d0>查看结果</div></div>',1)),e("div",Bo,[(h(!0),p(j,null,G(et(y.type).slice(0,3),m=>(h(),p("div",{key:m.id,class:F(["table-row",{retesting:m.isRetesting}])},[e("div",Do,[e("div",Po,r(Ft(m.startTime)),1)]),e("div",$o,[e("div",Oo,r(m.endTime?Ft(m.endTime):"-"),1)]),e("div",Io,[e("div",Mo,r(Re(m.duration||0)),1)]),e("div",zo,[m.status==="completed"?(h(),p("button",{key:0,onClick:Et=>Rt(m),class:"view-result-btn-small"}," 查看结果 ",8,Ao)):B("",!0)])],2))),128))]),et(y.type).length>3?(h(),p("div",Ro,[k(T,{to:`/test-history?type=${y.type}`,class:"view-more-link"},{default:mt(()=>[nt(" 查看全部 "+r(et(y.type).length)+" 次记录 ",1)]),_:2},1032,["to"])])):B("",!0)])])):B("",!0)])):B("",!0)]))),128))])])):B("",!0)]))),128))])]),e("section",Lo,[e("div",No,[n[18]||(n[18]=e("h2",{class:"section-title"},"历史测试记录",-1)),k(T,{to:"/test-history",class:"view-all-link"},{default:mt(()=>n[17]||(n[17]=[nt(" 查看全部记录 ",-1),e("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor"},[e("path",{d:"M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"})],-1)])),_:1,__:[17]})]),Object.keys(u.value).length>0?(h(),p("div",Fo,[(h(!0),p(j,null,G(u.value,(S,y)=>(h(),p("div",{key:y,class:"record-date-group"},[e("div",{class:"date-header",onClick:m=>Tt(y)},[e("div",Ho,[e("h3",jo,r(ut(y)),1),e("span",Vo,r(S.length)+" 次测试",1)]),e("div",Uo,[e("div",Wo,[n[19]||(n[19]=e("span",{class:"rate-text"},"完成率",-1)),e("span",Ko,r(At(S))+"%",1)]),At(S)===100?(h(),p("button",{key:0,onClick:St(m=>Ee(y,S),["stop"]),class:"task-report-btn"}," 📊 查看测试报告 ",8,Xo)):B("",!0),e("button",{class:F(["date-expand-btn",{expanded:f.value===y}])},n[20]||(n[20]=[e("svg",{width:"20",height:"20",viewBox:"0 0 24 24",fill:"currentColor"},[e("path",{d:"M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"})],-1)]),2)])],8,Yo),f.value===y?(h(),p("div",Go,[(h(!0),p(j,null,G(S,m=>{var Et;return h(),p("div",{key:m.id,class:"record-accordion-item"},[e("div",{class:"record-header",onClick:Xt=>ct(m.id)},[e("div",qo,[e("div",Qo,[e("span",null,r(tt(m.testType)),1)]),e("div",Jo,[e("h4",ta,r(m.testName),1),e("div",ea,[e("span",sa,r(Be(m.startTime)),1),m.duration?(h(),p("span",na," 用时 "+r(Math.round(m.duration/60))+" 分钟 ",1)):B("",!0),m.score?(h(),p("span",oa,r(m.score)+" 分 ",1)):B("",!0)])])]),e("div",aa,[e("span",{class:F(["record-status-badge",`status-${m.status}`])},r(X(m.status)),3),e("button",{class:F(["record-expand-btn",{expanded:_.value===m.id}])},n[21]||(n[21]=[e("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"currentColor"},[e("path",{d:"M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"})],-1)]),2)])],8,Zo),_.value===m.id?(h(),p("div",ia,[m.status==="completed"?(h(),p("div",la,[e("div",ra,[n[22]||(n[22]=e("span",{class:"summary-label"},"测试日期",-1)),e("span",ca,r(Lt(m.startTime)),1)]),m.endTime?(h(),p("div",ua,[n[23]||(n[23]=e("span",{class:"summary-label"},"结束日期",-1)),e("span",da,r(Lt(m.endTime)),1)])):B("",!0),m.score?(h(),p("div",va,[n[24]||(n[24]=e("span",{class:"summary-label"},"测试得分",-1)),e("span",ma,r(m.score)+" 分",1)])):B("",!0),(Et=m.results)!=null&&Et.overallPercentile?(h(),p("div",fa,[n[25]||(n[25]=e("span",{class:"summary-label"},"百分位数",-1)),e("span",pa,r(m.results.overallPercentile)+"%",1)])):B("",!0)])):B("",!0),e("div",ha,[m.status==="completed"?(h(),p("button",{key:0,onClick:Xt=>Rt(m),class:"view-result-btn"}," 📈 查看测试结果 ",8,ga)):B("",!0),m.status==="inProgress"?(h(),p("button",{key:1,onClick:Xt=>Se(m),class:"continue-btn"}," ▶️ 继续测试 ",8,_a)):B("",!0)])])):B("",!0)])}),128))])):B("",!0)]))),128))])):(h(),p("div",ya,[n[26]||(n[26]=e("div",{class:"empty-icon"},"📋",-1)),n[27]||(n[27]=e("p",{class:"empty-text"},"暂无测试记录",-1)),n[28]||(n[28]=e("p",{class:"empty-hint"},"完成测试后，记录将显示在这里",-1)),e("button",{onClick:n[2]||(n[2]=S=>l.value?De:Nt),class:"start-first-test-btn"},r(l.value?"开始重测":"开始主要测试"),1)]))])]),D.value?(h(),p("div",{key:0,class:"report-modal-overlay",onClick:xt},[e("div",{class:"report-modal",onClick:n[3]||(n[3]=St(()=>{},["stop"]))},[e("div",ba,[e("h3",ka,r((Ht=g.value)!=null&&Ht.isTaskReport?"📊":"📈")+" "+r((jt=g.value)!=null&&jt.isTaskReport?((Vt=g.value)==null?void 0:Vt.taskName)||((Ut=g.value)==null?void 0:Ut.testName):(Wt=g.value)==null?void 0:Wt.testName)+" "+r((Kt=g.value)!=null&&Kt.isTaskReport?"测试报告":"测试结果"),1),e("button",{onClick:xt,class:"report-modal-close","aria-label":"关闭报告"},n[29]||(n[29]=[e("svg",{width:"24",height:"24",viewBox:"0 0 24 24",fill:"currentColor"},[e("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})],-1)]))]),e("div",wa,[g.value?(h(),p("div",Ca,[e("div",Ta,[e("div",xa,[n[31]||(n[31]=e("div",{class:"metric-icon"},"🎯",-1)),e("div",Ea,[n[30]||(n[30]=e("div",{class:"metric-label"},"测试得分",-1)),e("div",Sa,r(g.value.score)+" 分",1)])]),e("div",Ba,[n[33]||(n[33]=e("div",{class:"metric-icon"},"📊",-1)),e("div",Da,[n[32]||(n[32]=e("div",{class:"metric-label"},"百分位数",-1)),e("div",Pa,r(g.value.percentile)+"%",1)])]),g.value.isTaskReport&&g.value.totalAttempts?(h(),p("div",$a,[n[35]||(n[35]=e("div",{class:"metric-icon"},"🔄",-1)),e("div",Oa,[n[34]||(n[34]=e("div",{class:"metric-label"},"测试次数",-1)),e("div",Ia,r(g.value.totalAttempts)+" 次",1)])])):B("",!0)]),e("div",Ma,[n[36]||(n[36]=e("h4",{class:"section-title"},"📋 成绩解释",-1)),e("p",za,r(g.value.interpretation),1)]),e("div",Aa,[n[39]||(n[39]=e("h4",{class:"section-title"},"📈 对比分析",-1)),e("div",Ra,[e("div",La,[n[37]||(n[37]=e("span",{class:"comparison-label"},"您的表现",-1)),e("span",{class:F(["comparison-status",g.value.comparisonData.isAboveAverage?"above-average":"below-average"])},r(g.value.comparisonData.isAboveAverage?"高于":"低于")+"平均水平 "+r(Math.abs(g.value.comparisonData.percentageDiff))+"% ",3)]),e("div",Na,[n[38]||(n[38]=e("span",{class:"comparison-label"},"同龄人平均分",-1)),e("span",Fa,r(g.value.comparisonData.averageScore)+" 分",1)])])]),g.value.recommendations&&g.value.recommendations.length>0?(h(),p("div",Ya,[n[40]||(n[40]=e("h4",{class:"section-title"},"💡 改进建议",-1)),e("ul",Ha,[(h(!0),p(j,null,G(g.value.recommendations,(S,y)=>(h(),p("li",{key:y,class:"recommendation-item"},r(S),1))),128))])])):B("",!0)])):B("",!0)]),e("div",{class:"report-modal-footer"},[e("button",{onClick:xt,class:"modal-btn modal-btn-primary"}," 知道了 ")])])])):B("",!0)])}}}),Ka=ss(ja,[["__scopeId","data-v-6c8341d0"]]);export{Ka as default};
